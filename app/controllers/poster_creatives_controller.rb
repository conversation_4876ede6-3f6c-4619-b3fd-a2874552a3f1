class PosterCreativesController < ApiController
  before_action :set_logged_in_user, except: [:preview_html]
  before_action :set_creative, only: [:preview_html]
  include Manifesto
  include KnowYourContestant
  include ElectionConstituencyStatus
  include CommonJsonBuilder
  include NewRelic::Agent::Instrumentation::ControllerInstrumentation

  def get_creatives_of_category
    category_id = nil
    category_id = params[:id].to_i if params[:id].present?
    category_kind = nil
    category_kind = params[:category_kind] if params[:category_kind].present?
    circle_id = nil
    circle_id = params[:circle_id].to_i if params[:circle_id].present?
    creative_id = nil
    creative_id = params[:creative_id].to_i if params[:creative_id].present?

    # to handle the edge case of info category (this might happen when user is already in posters tab where he can view
    # multiple party events but once he has given user role to a party, he can view only that party creatives not other)
    if circle_id.present? && category_kind.present? && category_kind.to_sym == :info
      user_role = @user.get_badge_role
      if user_role.present?
        affiliated_party_id = user_role.get_badge_user_affiliated_party_circle_id
        if affiliated_party_id.present? && affiliated_party_id != 0
          category_kind_circle = Circle.find_by(id: circle_id)
          if category_kind_circle.present? && category_kind_circle.political_party_level?
            return render json: { message: 'మీకు ఈ అనుమతి లేదు' },
                          status: :forbidden if affiliated_party_id != category_kind_circle.id
          elsif category_kind_circle.present? && category_kind_circle.political_leader_level?
            leader_affiliated_party_id = category_kind_circle.get_leader_circle_affiliated_party_id
            return render json: { message: 'మీకు ఈ అనుమతి లేదు' },
                          status: :forbidden if affiliated_party_id != leader_affiliated_party_id
          end
        end
      end
    end
    poster_creatives = @user.get_poster_creatives(category_id, category_kind, circle_id, creative_id)
    poster_layouts = []
    poster_layouts = @user.get_user_layouts(category_id:, circle_id:, category_kind:,
                                            creative_id:) unless poster_creatives.blank?

    # eliminate poster layouts which are not supported by posters tab v3 enabled app versions
    # for example older app versions don't support new basic poster layouts like basic_no_profile_pic_identity
    # so we need to eliminate those layouts from the response
    poster_layouts.reject! do |layout|
      @app_version < Constants.posters_tab_v3_enabled? && layout[:identity].present? &&
        Constants.posters_v3_released_identities.include?(layout[:identity][:type])
    end

    render json: { creatives: poster_creatives, layouts: poster_layouts, show_help: @user.is_poster_subscribed }
  end

  def dm_channel_poster_attachment_preview
    poster_params = PosterParams.from(params)
    event = poster_params.event
    include_paid = @user.is_eligible_for_premium_creatives?
    creatives = poster_params.get_creatives(include_paid:)
    title = event.present? ? event.name : ''
    circle_ids = poster_params.circle_ids
    active_creatives = poster_params.get_creatives(include_paid: include_paid, include_expired: false,
                                                   include_inactive: false)
    can_share_poster = false
    circle = nil
    deeplink = nil

    if active_creatives.blank?
      disable_cta_message = I18n.t('errors.posters_not_available')
    else
      circle_ids.each do |circle_id|
        user_circle_permissions = @user.get_all_circle_permissions(circle_id)
        if !can_share_poster && user_circle_permissions.map(&:to_sym).include?(:circle_share_poster)
          can_share_poster = true
          break
        end
      end
      disable_cta_message = can_share_poster ? nil : I18n.t('errors.posters_not_available_for_opposition')
    end

    if circle_ids.size == 1
      c = Circle.find_by(id: circle_ids.first)
      if c.present?
        circle = c.get_short_json
        # TODO: optimise this query
        circle[:analytics_params][:is_user_circle_member] = @user.circles.where(id: c.id).present?
      end
    end

    circle_params = {}
    circle_params = circle[:analytics_params] if circle.present?

    event_params = {}
    event_params = { event_id: event.id, event_name: event.name } if event.present?

    analytics_params = {}.merge(event_params).merge(circle_params).merge(poster_params.params)

    deeplink = "/posters/layout?source=chat&#{poster_params.params.to_query}"

    if creatives.blank?
      render json: { message: I18n.t('errors.posters_not_available') }, status: :not_found
    else
      render json: {
        title: title,
        image_urls: creatives.map { |c| c.photo_v3.url },
        params: poster_params.params,
        deeplink: deeplink,
        circle: circle,
        analytics_params: analytics_params,
        disable_cta_message: disable_cta_message,
        share_text: poster_params.share_text(@user.id),
      }, status: :ok
    end
  end

  def preview_html
    app_icon_url = 'https://a-cdn.thecircleapp.in/512x512/filters:quality(80)/production/admin-media/32/42ac9710-4924-4dc4-a262-90d96ac2c9a0.jpg'

    event = @creative.event
    circle_ids = []
    if event.present?
      circle_ids = event.event_circles.pluck(:circle_id)
    else
      circle_ids = @creative.poster_creative_circles.pluck(:circle_id)
    end

    circle = Circle.find_by_id(circle_ids.first) if circle_ids.length == 1

    if event.present? && circle.present?
      page_title = I18n.t('poster_preview.page_title_with_event_and_circle', event_name: event.name, circle_name: circle.name)
    elsif circle.present?
      page_title = I18n.t('poster_preview.page_title_with_circle', circle_name: circle.name)
    else
      page_title = I18n.t('poster_preview.page_title_default')
    end

    page_description = I18n.t('poster_preview.join_for_latest_updates')
    page_image = Metadatum.where(entity: @creative, key: 'creative_link_preview_image_url').first&.value
    # apply quality transform to use a smaller image
    page_image = Capture.apply_img_transform(page_image, width: 600, height: 315) if page_image.present?
    # fallback to app icon url if there is no preview image
    page_image = app_icon_url if page_image.blank?

    page_url = "https://m.praja.buzz/poster_creatives/#{@creative.id}/"
    page_type = 'article'

    locals = { page_title:, page_description:, page_image:, page_url:, page_type: }

    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/circles/preview.html.erb')),
      locals: locals
    )
    render html: html_content.html_safe
  end

  def set_creative
    if params[:poster_creative_id].present? && params[:poster_creative_id].to_i > 0
      @creative = PosterCreative.find_by(id: params[:poster_creative_id])
    end

    return render json: { message: I18n.t('errors.creative_not_found') },
                  status: :not_found unless @creative.present? && @creative.active?
  end

  def get_creatives
    set_pagination_params
    set_creative_params
    if @user.nil?
      return render json: {
        message: I18n.t('errors.permission_denied'), # Unauthorized
      }, status: :unauthorized
    end
    is_eligible_for_premium_creatives = @user.is_eligible_for_premium_creatives?
    sub_query_for_free_users = @user.get_sub_query_for_free_users(is_eligible_for_premium_creatives)

    if @category_kind.present? && @category_kind.to_sym == :manifesto && @circle_id.present?
      carousel = manifesto_carousel_next_page(user: @user, circle_id: @circle_id.to_i, offset: @offset, count: @count)
    elsif @category_kind.present? && @category_kind.to_sym == :congrats && @circle_id.present? && @level.present?
      carousel =
        if @level.to_sym == :MLA
          party_mla_congrats_next_page_carousel(user: @user, circle_id: @circle_id, offset: @offset, count: @count)
        elsif @level.to_sym == :MP
          party_mp_congrats_next_page_carousel(user: @user, circle_id: @circle_id, offset: @offset, count: @count)
        end
    elsif @category_kind.present? && @category_kind.to_sym == :congrats && @circle_id.present?
      carousel = user_district_assembly_winners_congrats_carousel(user: @user, district_id: @circle_id, offset: @offset, count: @count)
    elsif @category_id.present?
      event_hash = fetch_event_creatives(sub_query_for_free_users)
    elsif @category_kind.present? && @circle_id.present?
      @circle = Circle.find_by(id: @circle_id)
      creatives_hash = fetch_circle_creatives(sub_query_for_free_users)
    elsif @carousel_type.present? && @district_id.present? && @carousel_type.to_sym == :district_constituency_status_carousel
      carousel = district_constituency_status_carousel(user_id: @user.id, district_id: @district_id, offset: @offset, count: @count)
    elsif @carousel_type.present? && @state_id.present? && @carousel_type.to_sym == :state_constituency_status_carousel
      carousel = state_parliament_constituency_status_carousel(user_id: @user.id, state_id: @state_id, offset: @offset, count: @count)
    elsif @carousel_type.present?
      creatives_hash = fetch_contestant_creative_carousel_common(sub_query_for_free_users:)
    else
      return render json: { message: 'Invalid parameters' }, status: :bad_request
    end

    if carousel.present?
      return render json: carousel, status: :ok
    elsif event_hash.present?
      poster_creatives_json = event_hash[:poster_creatives_json]
      next_page_url = event_hash[:next_page_url]
      analytics_params = event_hash[:analytics_params]
      event = event_hash[:event]
    elsif creatives_hash.present?
      poster_creatives_json = creatives_hash[:poster_creatives_json]
      next_page_url = creatives_hash[:next_page_url]
      analytics_params = creatives_hash[:analytics_params]
    else
      return render json: { message: 'Invalid parameters' }, status: :bad_request
    end

    render json: { items: poster_creatives_json, next_page_url: next_page_url,
                   cta_text: nil, cta_deeplink: nil, cta_description: nil, analytics_params: analytics_params, },
           status: :ok
  end

  def fetch_contestant_creative_carousel_common(sub_query_for_free_users:)
    poster_creatives_json = []
    creative_kind = :contestant
    affiliated_leader_ids, contestant_circle_ids = affiliated_leader_with_contestant_ids(
      affiliated_party_id: @circle_id || @user.affiliated_party_circle_id, user: @user,
      carousel_type: @carousel_type
    )
    location_ids = fetch_location_ids_based_on_carousel_type(contestant_circle_ids: contestant_circle_ids,
                                                             carousel_type: @carousel_type, user: @user)
    circle_ids_with_poster_creatives, has_next_page, offset = PosterCreative.latest_creative_of_contestants(
      circle_ids: contestant_circle_ids,
      location_ids: location_ids,
      sub_query_for_free_users: sub_query_for_free_users,
      creative_kind: creative_kind, offset: @offset,
      count: @count
    )
    return [] unless circle_ids_with_poster_creatives.present?

    # build next page url if there are more creatives
    next_page_url = if has_next_page
                      params = { offset: offset, count: Constants.creatives_count,
                                 user_id: @user.id, carousel_type: @carousel_type.to_sym,
                                 circle_id: @circle_id, send_circle_cta: @send_circle_cta
                      }
                      build_next_page_url(params)
                    end
    require_poster_params = true
    circle_ids_with_poster_creatives.each do |circle_id, poster_creative|
      if @user.has_badge_role? && affiliated_leader_ids.exclude?(circle_id)
        require_poster_params = false
      end
      poster_creatives_json << poster_creative.get_json(category_kind: creative_kind, circle_id: circle_id,
                                                        require_poster_params: require_poster_params,
                                                        send_circle_cta: @send_circle_cta)
    end
    analytics_params = { kind: @category_kind, circle_ids: circle_ids_with_poster_creatives.keys }.compact
    build_response_hash(poster_creatives_json, next_page_url, analytics_params)
  end

  # Get posters feed with layouts
  # If frame_id is present in filter_data, the layout with that frame_id will be shown first only in the first feed item
  def get_posters_feed
    offset = params[:offset].to_i
    count = (params[:count] || 3).to_i
    loaded_feed_item_ids = params[:loaded_feed_item_ids] || []
    filter_data = params[:filter_data] || {}
    poster_creative_hash_array = []
    circle_id = filter_data[:circle_id].to_i if filter_data.present? && filter_data[:circle_id].present?
    event_id = filter_data[:id].to_i if filter_data.present? && filter_data[:id].present?
    creative_id = filter_data[:creative_id].to_i if filter_data.present? && filter_data[:creative_id].present?
    frame_id = filter_data[:frame_id].to_i if filter_data.present? && filter_data[:frame_id].present?

    # this is because we need to show a specific poster in the feed as first poster based on event_id or creative_id
    specific_doc_id = if event_id.present?
                        "event_#{event_id}"
                      else
                        creative_id.present? ? "creative_#{creative_id}" : nil
                      end

    parsed_loaded_feed_item_ids = loaded_feed_item_ids.present? ? loaded_feed_item_ids.map { |id| parse_poster_feed_item_id(id) } : []

    campaign = @user.user_eligible_1_year_campaign
    send_upgrade_feed_item, position_to_insert_upgrade_feed_item = get_insertion_details_for_upgrade_feed_item(loaded_feed_item_ids:, campaign_present: campaign.present?)
    upgrade_plan_feed_item = nil
    if send_upgrade_feed_item && ((campaign.present? && @user.show_upgrade_package_using_offer_feed_item_in_posters_feed?) || @user.show_upgrade_package_feed_item_in_posters_feed?)
      plan_details = fetch_plan_details_for_upgrade(user: @user)
      user_cohort = @user.user_cohort
      upgrade_plan_feed_item = build_upgrade_plan_json(user: @user, plan_details: plan_details, campaign: campaign,
                                                       user_cohort: user_cohort, send_as_feed_item: true)
    end

    count -= 1 if upgrade_plan_feed_item.present?

    poster_feed_data = PosterFeed.poster_feed_query(user: @user, per_page: count,
                                                    loaded_feed_item_ids: parsed_loaded_feed_item_ids,
                                                    circle_id: circle_id, specific_doc_id: specific_doc_id)

    image_creative_ids = []
    video_creative_ids = []

    poster_feed_data.each_value do |value|
      case value[:format_type]
      when "image"
        image_creative_ids.concat(value[:creative_ids])
      when "video"
        video_creative_ids.concat(value[:creative_ids])
      end
    end

    poster_creatives = PosterCreative.where(id: image_creative_ids.uniq)
    video_creatives = VideoCreative.where(id: video_creative_ids.uniq)

    return render json: { message: 'No posters found', feed_items: [] }, status: :ok if poster_creatives.blank? && video_creatives.blank?
    is_layout_locked = !@user.is_poster_subscribed

    poster_creatives.each do |poster_creative|
      poster_creative_hash_array << poster_creative.build_creative_json(user: @user, posters_tab_v3_enabled: true,
                                                                 is_layout_locked: is_layout_locked,
                                                                 is_eligible_for_premium_creatives: @user.is_poster_subscribed,
                                                                 category_kind: poster_creative.creative_kind,
                                                                 circle_id: circle_id,
                                                                 category_id: poster_creative.event_id,
                                                                 creative_id: poster_creative.id)

    end

    feed_items = []
    # if event_id or creative_id is present, then we need to show the specific poster as first poster in the feed
    # reorder the poster feed data hash based on the specific_doc_id
    if specific_doc_id.present?
      poster_feed_data = poster_feed_data.sort_by { |k, _| k == specific_doc_id ? 0 : 1 }.to_h
    end

    poster_feed_data.each do |k, v|

      if v[:format_type] == "image"
        # randomise the creative_ids to avoid showing the same poster in the same order
        v[:creative_ids] = v[:creative_ids].shuffle
        # sort the creative_ids based on show paid creative first followed by free creatives
        v[:creative_ids] = v[:creative_ids].sort_by { |id| poster_creatives.find { |c| c.id == id }.paid ? 0 : 1 }

        if creative_id.present?
          # if creative_id is present, then we need to show the specific creative as first creative in the creatives feed
          v[:creative_ids] = v[:creative_ids].sort_by { |id| id == creative_id ? 0 : 1 }
        end
        feed_creatives = v[:creative_ids].map { |cid| poster_creative_hash_array.find { |c| c[:id] == cid } }

        primary_creative_id = v[:primary_creative_id]
        primary_creative = poster_creatives.find { |c| c.id == primary_creative_id }
        category_id = primary_creative.event_id
        category_kind = primary_creative.creative_kind
        layouts = @user.get_user_layouts(category_id:, circle_id:, category_kind:, creative_id: primary_creative_id)
        feed_item_id = build_poster_feed_item_id(key: k, circle_id: circle_id)

        loaded_event_ids = parsed_loaded_feed_item_ids.select { |id| id.start_with?('event') }
                                                      .map { |id| id.split('_').last }.uniq
        if loaded_feed_item_ids.present?
          # layouts has array of hashes of each layout, in that we have frame type. based on frame type if frame_type is basic, move all them to first followed by all other
          # partition method divides the array into two arrays based on the condition and returns the two arrays
          # flatten method is used to convert the array of arrays to single array
          layouts = layouts.partition { |layout| layout[:frame_type] == 'basic' }.flatten
        end

        # If frame_id is present in filter_data and this is the first feed item,
        # find the layout with that frame_id and move it to the first position,
        # This ensures that the frame-related layout is shown first only in the first feed item
        if frame_id.present? && feed_items.empty? # Only for the first feed item
          frame_layout_index = layouts.find_index { |layout| layout[:id]&.to_i == frame_id }
          if frame_layout_index.present?
            # Remove the layout from its current position and insert it at the beginning
            frame_layout = layouts.delete_at(frame_layout_index)
            layouts.unshift(frame_layout)
          end
        end
        # for today sign up users (premium pitch should be present for 2nd and 6th,11th... position feed items only)
        # from next day onwards (1st,6th,11th... position feed items only)
        # for all others remove the premium pitch from the feed
        premium_pitch_layout = layouts.find { |layout| layout[:premium_pitch].present? }
        if premium_pitch_layout.present?
          # Remove the premium pitch layout from its current position
          layouts.delete(premium_pitch_layout)

          # Check if the user signed up today
          signed_up_today = @user.signed_up_today?
          # Insert premium pitch at 2nd position if specific_doc_id is present
          # Otherwise:
          # - Insert premium pitch at 1st and 6th,11th..... positions if user not signed up today
          # - Insert premium pitch at 2nd and 6th,11th..... positions if user signed up today
          if ((loaded_feed_item_ids.size % 5).zero? && !signed_up_today && specific_doc_id.blank?) ||
             (loaded_feed_item_ids.size == 1 && (signed_up_today || specific_doc_id.present?)) ||
             ((loaded_feed_item_ids.size % 5).zero? && loaded_feed_item_ids.size.positive? && signed_up_today)

            is_high_priority_event = primary_creative.event.present? && primary_creative.event.high_priority?
            if !is_high_priority_event && @user.eligible_for_self_trial?
              upcoming_nearest_high_priority_event = Event.upcoming_events(user: @user, only_one_high_event: true,
                                                                           circle_id_filter: circle_id,
                                                                           loaded_event_ids:)&.last
            end

            layouts.insert(0, premium_pitch_layout)

            # Only replace with high priority event if specific_doc_id is not present
            # or if the current item is not the specific_doc_id item
            if upcoming_nearest_high_priority_event.present? && (specific_doc_id.blank? || k != specific_doc_id)
              primary_creative_of_event = upcoming_nearest_high_priority_event.primary_creative
              feed_creatives = []
              feed_creatives << primary_creative_of_event.
                build_creative_json(user: @user, posters_tab_v3_enabled: true,
                                    is_layout_locked: is_layout_locked,
                                    is_eligible_for_premium_creatives: @user.is_poster_subscribed,
                                    category_kind: primary_creative_of_event.creative_kind,
                                    circle_id: circle_id,
                                    category_id: primary_creative_of_event.event_id,
                                    creative_id: primary_creative_of_event.id)
              # remove basic layouts for this upcoming event to avoid share
              layouts.reject! { |layout| layout[:frame_type] == 'basic' }
              feed_item_id = "event_#{upcoming_nearest_high_priority_event.id}"
            end
          end
        end

        loaded_feed_item_ids << feed_item_id
        feed_items << {
          feed_type: 'posters_feed_view_carousel',
          feed_item_id: feed_item_id,
          disable_screenshot: true,
          creatives: feed_creatives,
          layouts: layouts,
          analytics_params: {
            feed_item_id: feed_item_id
          }
        }
      elsif v[:format_type] == "video"
        primary_creative_id = v[:primary_creative_id]
        primary_creative = video_creatives.find { |c| c.id == primary_creative_id }

        video_mode = primary_creative.video.mode
        video_frame = VideoFrame.find_by(video_type: video_mode)
        user_video_frame = UserVideoFrame.where(user_id: @user.id, video_frame_id: video_frame.id, active: true).last

        frame_dimensions = user_video_frame.get_frame_dimensions(video_mode)

        elements = user_video_frame.get_video_creative_elements(primary_creative.video)

        video_poster_share_text = get_video_poster_share_text

        feed_items << {
          feed_type: 'video_posters_carousel',
          feed_item_id: build_poster_feed_item_id(key: k, circle_id: circle_id),
          video_frame_id: video_frame.id,
          video_creative_id: primary_creative_id,
          frame_height: frame_dimensions.dig(:height),
          frame_width: frame_dimensions.dig(:width),
          video_mode: video_mode,
          elements: elements,
          user_photo_type: "video_poster_cutout",
          video_poster_deeplink: '/posters',
          share_text: video_poster_share_text,
          analytics_params: nil
        }
      end
    end
    # if send_upgrade_feed_item is true and  position_to_insert_upgrade_feed_item is present and upgrade_plan_feed_item
    # is present then insert at that specific position by replacing the item at that position

    if send_upgrade_feed_item && position_to_insert_upgrade_feed_item.present? && upgrade_plan_feed_item.present?
      feed_items[position_to_insert_upgrade_feed_item] = upgrade_plan_feed_item
    end

    render json: { feed_items: feed_items.compact, analytics_params: nil }, status: :ok

  end

  private

  def get_insertion_details_for_upgrade_feed_item(loaded_feed_item_ids:, campaign_present:)
    num_loaded = loaded_feed_item_ids.size
    next_special_position = find_next_special_position_for_upgrade_feed_item(num_loaded, campaign_present)

    # Calculate the relative position in the next batch of 3 items
    if next_special_position <= num_loaded + 3
      relative_position = next_special_position - num_loaded - 1
      return [true, relative_position] if relative_position >= 0 && relative_position < 3
    end

    [false, nil]
  end

  # This method calculates the next position in the overall feed where the special item should appear
  def find_next_special_position_for_upgrade_feed_item(num_loaded, campaign_present)
    # if campaign is present, the special item should appear at every 5th position starting from 3rd position
    # if campaign is not present, the special item should appear at every 20th position starting from 21st position
    if campaign_present
      initial_position = 3
      step = 5
    else
      initial_position = 20
      step = 20
    end

    # Calculate the first expected special position beyond the current number of loaded items
    position = initial_position
    position += step while position <= num_loaded
    position
  end

  def build_poster_feed_item_id(key:, circle_id: nil)
    # if circle_id is present build the key as "key:#{circle_id} else "key:for_you"
    if circle_id.present?
      "#{key}:#{circle_id}"
    else
      "#{key}:for_you"
    end
  end

  # parse and remove circle_id or for_you which is present after : in the key
  def parse_poster_feed_item_id(feed_item_id)
    feed_item_id.split(':').first
  end

  def set_pagination_params
    @offset = params[:offset].to_i
    @count = params[:count].to_i
  end

  def set_creative_params
    @category_id = params[:id]
    @category_kind = params[:category_kind]
    @circle_id = params[:circle_id]
    @carousel_type = params[:carousel_type]
    @send_circle_cta = params[:send_circle_cta].to_s == 'true' if params[:send_circle_cta].present?
    @district_id = params[:district_id]
    @state_id = params[:state_id]
    @level = params[:level]
  end

  def fetch_event_creatives(sub_query_for_free_users)
    event = Event.find_by(id: @category_id, active: true)
    return nil unless event.present?
    poster_creatives, next_page_url = event.poster_creatives_with_next_page_url(sub_query_for_free_users: sub_query_for_free_users,
                                                                                offset: @offset, count: @count)
                                           .values_at(:poster_creatives, :next_page_url)
    poster_creatives_json = poster_creatives.map do |poster_creative|
      poster_creative.get_json(event:)
    end
    build_response_hash(poster_creatives_json, next_page_url, event.analytics_params, event:)
  end

  def fetch_circle_creatives(sub_query_for_free_users)
    creatives_hash = PosterCreative.category_kind_based_poster_creatives(circle_ids: [@circle_id],
                                                                         sub_query_for_free_users: sub_query_for_free_users,
                                                                         creative_kind: @category_kind, offset: @offset,
                                                                         count: @count)[@circle_id]
    return nil unless creatives_hash.present?
    poster_creatives, next_page_url = creatives_hash.values_at(:poster_creatives, :next_page_url)
    poster_creatives_json = poster_creatives.map do |poster_creative|
      poster_creative.get_json(category_kind: @category_kind, circle_id: @circle_id)
    end
    analytics_params = { circle_id: @circle_id, circle_name: @circle.name, kind: @category_kind }.compact
    build_response_hash(poster_creatives_json, next_page_url, analytics_params)
  end

  # return a hash which helps to build the response of get_creatives api
  def build_response_hash(poster_creatives_json, next_page_url, analytics_params, event: nil)
    {
      poster_creatives_json:,
      next_page_url:,
      analytics_params:,
      event:,
    }.compact
  end

  def get_video_poster_share_text
    share_text = "మీరు కూడా ఈ విధంగా *మీ వీడియో పోస్టర్* ని ఇప్పుడు *Praja App* లో పొందవచ్చు!" + "\n" +
                 "తెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు."
    share_text
  end

  add_transaction_tracer :get_posters_feed
end
