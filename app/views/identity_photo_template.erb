<!DOCTYPE html>
<html lang="te">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Badge Template</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@400;500;700&display=swap');

        @font-face {
            font-family: 'GIST TLOT Pavani Bold';
            src: url('https://cdn.thecircleapp.in/poster-fonts/GIST+TLOT+Pavani+Bold+Font.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        :root {
            --container-width: <%= container_width %>px;
            --container-height: <%= container_height %>px;
            --user-photo-width: <%= user_photo_width %>px;
            --name-font-size: <%= name_font_size %>px;
            --badge-font-size: <%= badge_font_size %>px;
            --name-font-family: '<%= name_font_family %>';
            --badge-font-family: '<%= badge_font_family %>';
        }

        #body {
            background: black;
        }

        #top-outer-container {
            width: var(--container-width);
            height: var(--container-height);
            margin: 20px auto;
            background: white;
            display: flex;
            align-items: center;
            flex-direction: row;
            justify-content: center;
            box-sizing: border-box;
            overflow: hidden;
        }

        #user-photo-placeholder {
            width: var(--user-photo-width);
            height: var(--container-height);
            background: white;
        }

        #outer-container {
            display: flex;
            align-items: center;
            flex-direction: column;
            flex-grow: 1;
            justify-content: center;
            box-sizing: border-box;
            overflow: hidden;
            min-width: 0;
            margin: auto;
        }

        #user-name {
            font-family: 'GIST TLOT Pavani Bold', sans-serif;
            font-size: var(--name-font-size);
            font-weight: 700;
            color: #015da0;
            word-wrap: break-word;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #badge-description {
            font-family: 'Noto Sans Telugu', sans-serif;
            font-size: var(--badge-font-size);
            font-weight: 500;
            color: black;
            margin: 0;
            word-wrap: break-word;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        #user-name, #badge-description {
            width: 100%; 
        }
       
    </style>
</head>

<body id="body">
    <div id="top-outer-container">
        <% if user_photo_width > 0 %>
        <div id="user-photo-placeholder"></div>
        <% end %>
        <div id="outer-container">
            <div id="user-name"><%= user_name %></div>
            <% if badge_description.present? %>
            <div id="badge-description"><%= badge_description %></div>
            <% end %>
        </div>
    </div>
</body>

</html>
