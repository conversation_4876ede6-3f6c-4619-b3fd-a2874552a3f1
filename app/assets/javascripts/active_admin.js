//= require active_admin/base
//= require active_admin/searchable_select
//= require activeadmin/dynamic_fields

const activeClass = 'active';
const inactiveClass = 'inactive'
const approvedItems = [];
const declinedItems = [];

// for the creation of poster creative we need to take whether the creative has event or not
function has_event_toggled() {
    var $checkbox = $('#has_event_checkbox');
    var $hasEventTarget = $('.has_event');
    var $noEventTarget = $('.no_event');
    var $paidCheckbox = $('#has_paid_checkbox');

    if (!$checkbox.is(':checked')) {
        $hasEventTarget.hide();
        //remove $hasEventTarget data
        // Clear the event selection field (Select2)
        var $eventSelect = $('#poster_creative_event_id');
        if ($eventSelect.length) {
            $eventSelect.val(null).trigger('change');
        }

        $noEventTarget.show();
        // Disable paid option for non-event poster creatives
        $paidCheckbox.prop('disabled', true);
        $paidCheckbox.prop('checked', false);
        // Add a note explaining why it's disabled
        if (!$paidCheckbox.next('.paid-disabled-note').length) {
            $paidCheckbox.parent().append('<span class="paid-disabled-note" style="color: #d45252; margin-left: 10px; font-style: italic;">Paid option is only available for event poster creatives</span>');
        }
    } else {
        $hasEventTarget.show();
        $noEventTarget.hide();
        // Enable paid option for event poster creatives
        $paidCheckbox.prop('disabled', false);
        // Remove the note
        $paidCheckbox.parent().find('.paid-disabled-note').remove();
    }
}

$(document).ready(function () {
    // Call has_event_toggled on page load to set initial state
    if ($('#has_event_checkbox').length) {
        has_event_toggled();
    }

    // Aspect ratio validation for OE Work Flow photo uploads
    if (window.location.pathname.includes('/oe_work_flow')) {
        // Variable to track if we're currently updating buttons to prevent loops
        let isUpdatingButtons = false;

        // Override the existing updateButtons function to integrate with our aspect ratio checks
        window.updateButtons = function () {
            // Prevent recursive calls
            if (isUpdatingButtons) return;
            isUpdatingButtons = true;

            try {
                // 1. Get all relevant elements
                // Buttons
                const submitButton = $('input[type="submit"]:not(.incomplete-btn)')[0];
                const incompleteButton = $('.incomplete-btn')[0];
                const submitWarning = $('.submit-warning')[0];
                const form = $('form.formtastic')[0];

                // File inputs
                const posterPhotoInput = $('input[name="oe_work_flow[user_poster_photo]"]')[0];
                const familyPhotoInput = $('input[name="oe_work_flow[family_frame_photo]"]')[0];
                const heroPhotoInput = $('input[name="oe_work_flow[hero_frame_photo]"]')[0];

                // Remarks and requested circles (using the original logic)
                const totalRequested = $('.requested-circle-row').length;
                const totalHidden = $('#requested_circles_hidden_inputs input').length;
                const circlesReady = (totalRequested === 0 || totalHidden === totalRequested);
                const hasRequestedCircles = totalRequested > 0;
                const remarksExists = $('.remarks-textarea').length > 0;
                const remarksFilled = remarksExists ? $('.remarks-textarea').val().trim().length > 0 : false;

                // Aspect ratio messages
                const aspectRatioWarnings = $('.aspect-ratio-warning');
                const allAspectRatioInfos = $('.aspect-ratio-info');

                // 2. Handle aspect ratio messages for files with new uploads

                // We'll only hide info messages in the updateButtons function
                // Warnings and info messages are removed when the file input changes (see the change event handler)
                // This ensures we don't remove warnings that were just added by validation

                // For each photo input with a file, we'll temporarily hide any info messages
                // This allows the submit button to be enabled when new files are uploaded
                if (posterPhotoInput && posterPhotoInput.files && posterPhotoInput.files.length > 0) {
                    // Temporarily hide info messages for this input
                    $(".poster-photo-without-bg-label").next(".aspect-ratio-info").hide();
                    $(".poster-photo-without-bg-label").next(".dummy-photo-info").hide();
                }

                if (familyPhotoInput && familyPhotoInput.files && familyPhotoInput.files.length > 0) {
                    // Temporarily hide info messages for this input
                    $(".family-frame-photo-label").next(".aspect-ratio-info").hide();
                    $(".family-frame-photo-label").next(".dummy-photo-info").hide();
                }

                if (heroPhotoInput && heroPhotoInput.files && heroPhotoInput.files.length > 0) {
                    // Temporarily hide info messages for this input
                    $(".hero-frame-photo-label").next(".aspect-ratio-info").hide();
                    $(".hero-frame-photo-label").next(".dummy-photo-info").hide();
                }

                // 3. Determine button states based on all conditions
                // Get visible aspect ratio and dummy-photo-info messages
                // Note: We now disable buttons for both warnings, info, and dummy-photo-info messages
                const visibleAspectRatioInfos = $(".aspect-ratio-info:visible");
                const visibleAspectRatioWarnings = $(".aspect-ratio-warning:visible");
                const visibleDummyPhotoInfos = $(".dummy-photo-info:visible");
                const hasAspectRatioOrDummyIssues =
                    visibleAspectRatioInfos.length > 0 ||
                    visibleAspectRatioWarnings.length > 0 ||
                    visibleDummyPhotoInfos.length > 0;

                // 4. Create a specific warning message based on actual conditions
                let warningMessage = '';
                let submitDisabledReasons = [];

                // Collect reasons why submit button is disabled
                if (hasRequestedCircles && !circlesReady) {
                    submitDisabledReasons.push("requested circles are present");
                }

                if (remarksFilled) {
                    submitDisabledReasons.push("remarks are filled");
                }

                if (hasAspectRatioOrDummyIssues) {
                    if (visibleAspectRatioWarnings.length > 0) {
                        submitDisabledReasons.push(
                            "some newly uploaded images don't have the correct dimensions of 250px width and 280px height"
                        );
                    } else if (visibleAspectRatioInfos.length > 0) {
                        submitDisabledReasons.push(
                            "some existing images don't have the correct dimensions of 250px width and 280px height"
                        );
                    }
                    if (visibleDummyPhotoInfos.length > 0) {
                        submitDisabledReasons.push(
                            "some images have failed background removal and require a new image with the background already removed"
                        );
                    }
                }

                // Format the submit button warning message
                if (submitDisabledReasons.length > 0) {
                    warningMessage = "*Submit button is disabled because " + submitDisabledReasons.join(" and ") + ". ";

                    // Add additional instruction for aspect ratio or dummy-photo-info issues
                    if (hasAspectRatioOrDummyIssues) {
                        warningMessage +=
                            "Please resolve the image issues (resize to 250x280px or upload a new image with background already removed).";
                    }
                }

                // Add incomplete button warning if needed
                if (!remarksFilled) {
                    if (warningMessage !== "") {
                        warningMessage += " ";
                    }
                    warningMessage += "Incomplete button is disabled because remarks are required.";
                }

                // 5. Apply button states based on all conditions
                // For Submit button: Need circlesReady AND !remarksFilled AND !hasAspectRatioOrDummyIssues
                if (circlesReady && !remarksFilled && !hasAspectRatioOrDummyIssues) {
                    $('input[type="submit"]:not(.incomplete-btn)').prop("disabled", false);
                } else {
                    $('input[type="submit"]:not(.incomplete-btn)').prop("disabled", true);
                }

                // For Incomplete button: Only need remarksFilled
                // Since we're only saving remarks when Incomplete is clicked, we don't need to check for aspect ratio or dummy-photo-info issues
                if (remarksFilled) {
                    $(".incomplete-btn").prop("disabled", false);
                } else {
                    $(".incomplete-btn").prop("disabled", true);
                }

                // 6. Update warning message
                if (warningMessage !== "") {
                    // Show warning with the combined message
                    if ($(".submit-warning").length > 0) {
                        // If the warning div exists, update its content and show it
                        $(".submit-warning").html(warningMessage).show();
                    } else {
                        // If the warning div doesn't exist, create one near the submit buttons
                        const form = $("form.formtastic");
                        if (form.length > 0) {
                            const actions = form.find(".actions, .form-actions");
                            if (actions.length > 0) {
                                const warningDiv = $(
                                    '<div class="submit-warning" style="color: red; margin-top: 10px;">' +
                                    warningMessage +
                                    "</div>"
                                );
                                actions.before(warningDiv);
                            }
                        }
                    }
                } else {
                    // Hide warning if everything is good
                    $('.submit-warning').hide();
                }
            } catch (error) {
                console.error('Error in updateButtons:', error);
            } finally {
                // Always release the lock
                isUpdatingButtons = false;
            }
        };

        // Function to check if uploaded files have the correct dimensions (250px width and 280px height)
        // or the correct aspect ratio (25:28)
        function checkAspectRatio(file, fieldName) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function () {
                    const width = this.width;
                    const height = this.height;
                    const ratio = (width / height).toFixed(2);
                    const expectedRatio = (25 / 28).toFixed(2);
                    const isValid = Math.abs(ratio - expectedRatio) < 0.01;
                    resolve({isValid: isValid, fieldName: fieldName});
                };
                img.onerror = function () {
                    reject(new Error("Failed to load image"));
                };
                img.src = URL.createObjectURL(file);
            });
        }

        // Function to show error messages for all invalid photos
        function showErrors(invalidResults) {
            // Clear any existing error messages
            $('.aspect-ratio-warning').remove();

            // Map field names to their corresponding CSS classes
            const fieldToClassMap = {
                'Poster Photo Without Background': '.poster-photo-without-bg-label',
                'Family Frame Photo': '.family-frame-photo-label',
                'Hero Frame Photo': '.hero-frame-photo-label',
                'Poster Photo With Background': '.poster-photo-with-bg-label'
            };

            // Process each invalid result
            invalidResults.forEach((result, index) => {
                const fieldName = result.fieldName;

                // Create error message
                const errorDiv = $('<div class="aspect-ratio-warning"><span>Warning: ' + fieldName + ' does not have the required dimensions of 250px width and 280px height</span></div>');

                // Get the CSS selector for this field
                const selector = fieldToClassMap[fieldName];

                // Find the target label using the CSS selector
                if (selector && $(selector).length > 0) {
                    // Insert after the label div
                    $(selector).after(errorDiv);

                    // Only scroll to the first error message
                    if (index === 0) {
                        $('html, body').animate({
                            scrollTop: errorDiv.offset().top - 100
                        }, 500);
                    }
                } else {
                    // Fallback to the first poster-photo-label if we couldn't find a specific one
                    if ($('.poster-photo-label').length > 0) {
                        // Insert after the first label div
                        $('.poster-photo-label').first().after(errorDiv);

                        // Only scroll to the first error message
                        if (index === 0) {
                            $('html, body').animate({
                                scrollTop: errorDiv.offset().top - 100
                            }, 500);
                        }
                    }
                }
            });

            // Call updateButtons to update button states
            updateButtons();
        }

        // Run updateButtons on page load
        $(document).ready(function () {
            // Wait a bit for the page to fully load
            setTimeout(updateButtons, 500);
        });

        // Set up a MutationObserver to watch for changes to specific parts of the DOM
        const observer = new MutationObserver(function (mutations) {
            // Only update if mutations affect aspect ratio warnings/info or form elements
            const shouldUpdate = mutations.some(mutation => {
                // Check if the mutation target or any of its children have the relevant classes
                return $(mutation.target).hasClass('aspect-ratio-warning') ||
                    $(mutation.target).hasClass('aspect-ratio-info') ||
                    $(mutation.target).find('.aspect-ratio-warning, .aspect-ratio-info').length > 0 ||
                    mutation.target.tagName === 'FORM' ||
                    mutation.target.tagName === 'INPUT' ||
                    $(mutation.target).hasClass('formtastic') ||
                    $(mutation.target).hasClass('remarks-textarea') ||
                    $(mutation.target).hasClass('requested-circle-row');
            });

            if (shouldUpdate) {
                setTimeout(updateButtons, 500);
            }
        });

        // Start observing the form and its elements
        if ($("form.formtastic").length > 0) {
            observer.observe($("form.formtastic")[0], {childList: true, subtree: true, attributes: true});
        }

        // Note: Using the showErrors function defined earlier

        // Function to validate all files before form submission
        function validateFiles(event) {
            const form = event.target.closest("form");

            // Check if the Incomplete button was clicked
            // If so, skip validation since we're only saving remarks
            if (event.target.classList.contains("incomplete-btn")) {
                // Allow form submission without validation
                return true;
            }

            const posterPhotoInput = form.querySelector('input[name="oe_work_flow[user_poster_photo]"]');
            const familyPhotoInput = form.querySelector('input[name="oe_work_flow[family_frame_photo]"]');
            const heroPhotoInput = form.querySelector('input[name="oe_work_flow[hero_frame_photo]"]');

            // Check if any files are selected
            const posterFile = posterPhotoInput && posterPhotoInput.files.length > 0 ? posterPhotoInput.files[0] : null;
            const familyFile = familyPhotoInput && familyPhotoInput.files.length > 0 ? familyPhotoInput.files[0] : null;
            const heroFile = heroPhotoInput && heroPhotoInput.files.length > 0 ? heroPhotoInput.files[0] : null;

            // If no files are selected, allow form submission
            if (!posterFile && !familyFile && !heroFile) {
                return true;
            }

            // Prevent default form submission
            event.preventDefault();

            // Note: We don't remove warnings/infos here anymore
            // That's now handled in the file input change event handler
            // This ensures we don't remove warnings/infos at the wrong time

            // Array to hold promises for all file validations
            const validations = [];

            if (posterFile) {
                validations.push(checkAspectRatio(posterFile, "Poster Photo Without Background"));
            }
            if (familyFile) {
                validations.push(checkAspectRatio(familyFile, "Family Frame Photo"));
            }
            if (heroFile) {
                validations.push(checkAspectRatio(heroFile, "Hero Frame Photo"));
            }

            // Check all files
            Promise.all(validations)
                .then((results) => {
                    // Find all files with invalid aspect ratio
                    const invalidResults = results.filter((result) => !result.isValid);

                    if (invalidResults.length > 0) {
                        // Show errors for all invalid files
                        showErrors(invalidResults);
                        // updateButtons is already called inside showErrors
                    } else {
                        // All files are valid, update buttons and submit the form
                        updateButtons();
                        form.submit();
                    }
                })
                .catch((error) => {
                    console.error("Error validating images:", error);
                    // Allow form submission on error
                    form.submit();
                });

            return false;
        }

        // Attach validation to form submission buttons
        const submitButtons = document.querySelectorAll('input[type="submit"]');
        submitButtons.forEach((button) => {
            button.addEventListener("click", validateFiles);
        });

        // Add event listeners to file inputs to update buttons when files are selected
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach((input) => {
            input.addEventListener("change", function () {
                // Check if this is one of our photo inputs
                if (this.name === "oe_work_flow[user_poster_photo]") {
                    // Remove any existing warnings or info messages for this input
                    $(".poster-photo-without-bg-label")
                        .next(".aspect-ratio-info, .aspect-ratio-warning, .dummy-photo-info")
                        .remove();
                    // Update buttons after a short delay to allow for any other changes
                    setTimeout(updateButtons, 100);
                } else if (this.name === "oe_work_flow[family_frame_photo]") {
                    // Remove any existing warnings or info messages for this input
                    $(".family-frame-photo-label")
                        .next(".aspect-ratio-info, .aspect-ratio-warning, .dummy-photo-info")
                        .remove();
                    // Update buttons after a short delay to allow for any other changes
                    setTimeout(updateButtons, 100);
                } else if (this.name === "oe_work_flow[hero_frame_photo]") {
                    // Remove any existing warnings or info messages for this input
                    $(".hero-frame-photo-label")
                        .next(".aspect-ratio-info, .aspect-ratio-warning, .dummy-photo-info")
                        .remove();
                    // Update buttons after a short delay to allow for any other changes
                    setTimeout(updateButtons, 100);
                }

                // Check if all warnings and info messages are gone
                setTimeout(() => {
                    const visibleAspectRatioInfos = $(".aspect-ratio-info:visible");
                    const visibleAspectRatioWarnings = $(".aspect-ratio-warning:visible");
                    const visibleDummyPhotoInfos = $(".dummy-photo-info:visible");
                    if (
                        visibleAspectRatioInfos.length === 0 &&
                        visibleAspectRatioWarnings.length === 0 &&
                        visibleDummyPhotoInfos.length === 0
                    ) {
                        // If no warnings or info messages are visible, hide the submit warning
                        $(".submit-warning").hide();
                    }
                    updateButtons();
                }, 200);
            });
        });

        // Run an initial check for aspect ratio issues on page load
        document.addEventListener("DOMContentLoaded", function () {
            // Force a check after the page has fully loaded
            // Using a single timeout with a reasonable delay
            setTimeout(updateButtons, 500);

            // If remarks field exists, bind its events to update buttons
            var $remarks = $(".remarks-textarea");
            if ($remarks.length > 0) {
                $remarks.on("keyup change", function () {
                    updateButtons();
                });
            }
        });
    }
});

$(document).ready(function () {
    let isSelectCircleDialog = false;
    var idInput = $("#id_input");
    var entityTypeInput = $("#entity_type_input");
    var entityIdInput = $("#entity_id_input");

    let id = idInput.val();
    let entityType = entityTypeInput.val();
    let entityId = entityIdInput.val();
    let circlePhotoInfo = {};
    let circleIdPhotoMap = {};

    // On entity type change call the function to show or hide the user poster photos
    entityTypeInput.on("input", function () {
        entityType = $(this).val();
        toggleUserPosterPhotos();
    });

    // On entity id change call the function to show or hide the count sections
    entityIdInput.on("input", function () {
        entityId = $(this).val();
        toggleCountSections();
    });

    toggleUserPosterPhotos();
    toggleCountSections();

    // Don't show user poster photos for circle entity type
    function toggleUserPosterPhotos() {
        //for Circle type don't show else hide
        const display = entityType === "Circle" ? "none" : "block";
        $(".user_poster_photos").css("display", display);
    }

    // show or hide the count sections based on entity id
    function toggleCountSections() {
        const display = entityId ? "block" : "none";
        // Show if entity id is present else hide
        $(".h1_count_section, .h2_count_section").css("display", display);
    }

    // Function to update the buttons and photo containers based on the input value
    function updateButtons(inputSelector, containerSelector, buttonClass, photoContainerClass, maxValue) {
        const inputElement = $(inputSelector);
        let count = parseInt(inputElement.val()) || 0;
        count = count > maxValue ? maxValue : count;
        inputElement.val(count);

        const container = $(containerSelector);
        // Show the name in the button based on the class either H1 or H2
        const buttonName = buttonClass === "h1_item_button" ? "H1" : "H2";
        // Just to show the difference in view for both header 1 and header 2 photos set different sizes
        const photoSize = buttonClass === "h1_item_button" ? "100px" : "75px";
        container.html("");
        for (let i = 0; i < count; i++) {
            const button = $(
                `<button type='button' class='${buttonClass}' data-index='${i}'>${buttonName} Photo ${i + 1}</button>`
            );
            const deleteCheckBox = $(
                `<input type='checkbox' class='delete_${buttonClass}' data-index='${i}'> Delete</input>`
            );
            const photoContainer = $(`<div class='${photoContainerClass}' data-index='${i}'></div>`);
            // Also on update of buttons itself
            // Make sure to get the photo url and circle id (as need to show the buttons already saved data on edit page)
            $.ajax({
                url: "/admin/user_poster_layouts/get_layout_header_circle_photo_url",
                data: {
                    id: id,
                    header_type: buttonName,
                    priority: i,
                },
                type: "GET",
                success: function (response) {
                    const button = $(`.` + `${buttonClass}` + `[data-index='${i}']`);
                    const circleId = response["circle_id"] || "";
                    const photoUrl = response["circle_photo_url"] || "";
                    const photoId = response["circle_photo_id"] || "";
                    const priority = response["priority"] || "";
                    if (photoId) {
                        button.data("photo-id", photoId);
                    }
                    if (circleId) {
                        button.data("circle-id", circleId);
                    }
                    if (photoUrl) {
                        photoContainer.html(
                            `<img src="${photoUrl}" alt="Circle Photo" style="width: ${photoSize}; height: ${photoSize};">`
                        );
                    }
                    if (priority) {
                        button.data("priority", priority);
                    }
                },
            });
            container.append(button, deleteCheckBox, photoContainer);
        }
    }

    // on input change to h1 count update the buttons and photo containers
    $("#h1_count_input").on("input", function () {
        updateButtons("#h1_count_input", "#dynamic_buttons_h1", "h1_item_button", "h1_photo_container", 3);
    });

    // on input change to h2 count update the buttons and photo containers
    $("#h2_count_input").on("input", function () {
        updateButtons("#h2_count_input", "#dynamic_buttons_h2", "h2_item_button", "h2_photo_container", 10);
    });

    // Initialise buttons and photo containers to be shown
    updateButtons("#h1_count_input", "#dynamic_buttons_h1", "h1_item_button", "h1_photo_container", 3);
    updateButtons("#h2_count_input", "#dynamic_buttons_h2", "h2_item_button", "h2_photo_container", 10);

    // Show active admin modal dialog for selecting circle on click of any button with class h1_item_button or h2_item_button
    function handleButtonClick(buttonSelector, photoContainerClass, photoSize) {
        $(document).on("click", buttonSelector, function () {
            isSelectCircleDialog = true;
            const buttonIndex = $(this).data("index");
            ActiveAdmin.ModalDialog("Select Circle", {"Circle ID": []}, function (inputs) {
                const circleId = inputs["Circle ID"];
                const button = $(buttonSelector + `[data-index='${buttonIndex}']`);
                button.data("circle-id", circleId);
                button.data("photo-id", circleIdPhotoMap[circleId]["photoId"]);

                // to show the photo that is being already selected in dialog box to be shown in the page
                let photoContainer = $(`.${photoContainerClass}[data-index='${buttonIndex}']`);
                photoContainer.html(
                    `<img src="${circleIdPhotoMap[circleId]["photoUrl"]}" alt="Circle Photo" style="width: ${photoSize}; height: ${photoSize};">`
                );
            });
        });
    }

    // for delete checkbox handle click set the checkbox to be checked and data of the checkbox index if the button is checked
    function handleCheckboxClick(checkboxSelector) {
        $(document).on("change", checkboxSelector, function () {
            const checkbox = $(this);
            const buttonIndex = checkbox.data("index");
            if (checkbox.prop("checked")) {
                $(`${checkboxSelector}[data-index='${buttonIndex}']`).prop("checked", true);
            }
        });
    }

    // Close the dialog box on click of close or ok button in the dialog box
    $(document).on("click", ".ui-dialog-titlebar-close, .ui-dialog-buttonpane button.ui-button", function () {
        setTimeout(function () {
            $("#dialog_confirm").remove();
        }, 100);
    });

    // Initialize click handlers for h1 and h2 buttons
    handleButtonClick(".h1_item_button", "h1_photo_container", "100px");
    handleButtonClick(".h2_item_button", "h2_photo_container", "75px");

    // Initialize click handlers for delete checkboxes for both h1 and h2
    handleCheckboxClick(".delete_h1_item_button");
    handleCheckboxClick(".delete_h2_item_button");

    // Before the dialog opens make sure the create circle button is removed
    $("body").on("modal_dialog:before_open", function () {
        $("#createCircleBtn").remove();
    });

    $("body").on("modal_dialog:after_open", function () {
        var selectedCircleId;
        if (isSelectCircleDialog) {
            // Append a dialog footer to the dialog box, so that we can append create button in it
            if ($("#dialog_confirm_footer").length === 0) {
                $("#dialog_confirm").append('<div id="dialog_confirm_footer"></div>');
            }

            let lastSearchTerm = "";
            setTimeout(function () {
                // Make the select to select2 input for searchable select input
                $("#dialog_confirm select")
                    .select2({
                        multiple: true,
                        maximumSelectionLength: 1,
                        placeholder: "Search a circle",
                        allowClear: true,
                        ajax: {
                            url: function (params) {
                                return params.term && params.term.trim() !== ""
                                    ? "/admin/user_poster_layouts/get_es_circles"
                                    : "/admin/user_poster_layouts/suggesting_circles_of_user";
                            },
                            dataType: "json",
                            delay: 100,
                            data: function (params) {
                                lastSearchTerm = params.term;
                                // Query parameters will be passed to the api function (entity info is passed to get the circles related to particular User)
                                return {
                                    term: params.term,
                                    entity_type: entityType,
                                    entity_id: entityId,
                                };
                            },
                            processResults: function (data) {
                                // return the result from the search (will be showed in the return format)
                                return {
                                    results: data.map(function (item) {
                                        let displayText = item.name + " ," + item.name_en;
                                        if (item.party_short_name) {
                                            displayText += " (" + item.party_short_name + ")";
                                        }
                                        return {id: item.id, text: displayText};
                                    }),
                                };
                            },
                            cache: true,
                        },
                    })
                    .on("select2:select", function (e) {
                        let photoContainer = $("#dialog_confirm_footer .photo-options");
                        // On circle select
                        // We need to show photos of the circle
                        // So add a photo container if it doesn't exist
                        if (photoContainer.length === 0) {
                            $("#dialog_confirm_footer").prepend('<div class="photo-options"></div>');
                            photoContainer = $("#dialog_confirm_footer .photo-options");
                        }
                        photoContainer.empty();

                        selectedCircleId = e.params.data.id;

                        // Fetch photos of the selected circle as an api, get id and url of the photo
                        $.ajax({
                            url: "/admin/user_poster_layouts/get_circle_poster_photo_urls",
                            data: {circle_id: selectedCircleId},
                            type: "GET",
                            success: function (response) {
                                let circlePhotoUrls = response["circlePhotoUrls"];
                                let circlePhotoIds = response["circlePhotoIds"];

                                circlePhotoInfo[selectedCircleId] = [];

                                for (let i = 0; i < circlePhotoIds.length; i++) {
                                    let photoUrl = circlePhotoUrls[i];
                                    let photoId = circlePhotoIds[i];
                                    if (photoUrl && photoId) {
                                        circlePhotoInfo[selectedCircleId].push({
                                            url: photoUrl,
                                            id: photoId,
                                        });
                                    }
                                }

                                displayPhotoOptions(selectedCircleId, photoContainer);
                            },
                        });
                    });

                function displayPhotoOptions(circleId, photoContainer) {
                    var photoInfo = circlePhotoInfo[circleId];
                    photoContainer.empty();
                    var photoRowContainer = $('<div class="circle-photo-row-container"></div>');
                    var photoCount = 0;

                    // Those photos will be showed under the search input field
                    // In the form of radio button selection
                    var photoSection = $(
                        '<div class="circle-photo-container" style="display: flex; justify-content: space-between; margin-bottom: 10px;"></div>'
                    );
                    photoInfo.forEach(function (photo, index) {
                        photoSection.append(createPhotoLabel(photo.url, photo.id)); // Pass both URL and ID
                        photoCount++;

                        if (photoCount % 4 === 0) {
                            photoRowContainer.append(photoSection);
                            photoSection = $(
                                '<div class="circle-photo-container" style="display: flex; justify-content: space-between; margin-bottom: 10px;"></div>'
                            );
                        }
                    });

                    // Append any remaining photos
                    if (photoSection.children().length > 0) {
                        photoRowContainer.append(photoSection);
                    }
                    photoContainer.html(photoRowContainer);
                }

                function createPhotoLabel(photoUrl, photoId) {
                    return $('<label style="display: flex; align-items: center;">')
                        .append(
                            $(
                                '<input type="radio" name="photoSelect" value="' +
                                photoId +
                                "|" +
                                photoUrl +
                                '" style="margin-right: 5px;">'
                            )
                        )
                        .append(
                            `<img src="${photoUrl}" alt="Selected Circle Photo" style="width: 100px; height: 100px;">`
                        );
                }

                // As footer has photo options, on selection of a photo need to store the selected photo id and url
                $("#dialog_confirm_footer").on("change", 'input[name="photoSelect"]', function () {
                    const value = $(this).val();
                    const parts = value.split("|");
                    const selectedPhotoId = parts[0];
                    const selectedPhotoUrl = parts[1];

                    circleIdPhotoMap[selectedCircleId] = {
                        photoId: selectedPhotoId,
                        photoUrl: selectedPhotoUrl,
                    };
                });

                $("#dialog_confirm .select2-container").css("width", "100%");

                // Make sure create circle button is removed before adding it
                $("#createCircleBtn").remove();
                // Add a create circle button to the dialog box
                const button = $('<button id="createCircleBtn">Create Circle</button>');

                // on click of create circle button open a new tab with the create circle page
                // Need to be pre filled with the search term and entity info
                button.on("click", function (event) {
                    event.preventDefault();
                    let url = "/admin/circles/new";
                    const params = new URLSearchParams({
                        circle_type: "interest",
                        level: "political_leader",
                    });
                    if (lastSearchTerm && lastSearchTerm.trim() !== "") {
                        params.append("name", lastSearchTerm);
                        params.append("name_en", lastSearchTerm);
                    }
                    params.append("entity_type", entityType);
                    params.append("entity_id", entityId);
                    url += `?${params.toString()}`;
                    window.open(url, "_blank");
                });

                // Append the create circle button to the dialog box in the dialog footer which has been appended earlier
                $("#dialog_confirm_footer").append(button);
                $("#dialog_confirm select").select2("open");
            }, 10);
        }
    });

    // on submit of the dialog box set the data
    $("form").on("submit", function (e) {
        // prevent default submit of data
        e.preventDefault();

        // For activate and preview buttons
        const clickedButton = $('input[type="submit"]:focus').attr("name");
        const submitValue = $('input[type="submit"]:focus').val();
        $("<input>")
            .attr({
                type: "hidden",
                name: clickedButton,
                value: submitValue,
            })
            .appendTo($(this));

        // For h1 and h2 buttons
        const appendHiddenInputs = (selector, buttonType) => {
            $(selector).each(function () {
                const circleId = $(this).data("circle-id");
                const photoId = $(this).data("photo-id");
                const priority = $(this).data("priority");

                if (photoId) {
                    const idObject = {
                        circle_id: circleId === undefined ? null : parseInt(circleId),
                        photo_id: parseInt(photoId),
                        priority: parseInt(priority),
                    };

                    const jsonValue = JSON.stringify(idObject);

                    $("<input>", {
                        type: "hidden",
                        name: `buttons[${buttonType}][]`,
                        value: jsonValue,
                    }).appendTo($("form"));
                }
            });
        };

        // For delete h1 and h2 buttons
        const appendHiddenInputsForDelete = (selector, buttonType) => {
            $(selector).each(function () {
                const index = $(this).data("index");
                const checkBoxValue = $(this).prop("checked");
                if (checkBoxValue) {
                    $("<input>", {
                        type: "hidden",
                        name: `delete_buttons[${buttonType}][]`,
                        value: index,
                    }).appendTo($("form"));
                }
            });
        };

        // Data selected for h1 and h2 buttons, append the data into the form
        appendHiddenInputs(".h1_item_button", "h1");
        appendHiddenInputs(".h2_item_button", "h2");

        // Data selected for delete h1 and h2 buttons, append the data into the form (which is primarily checkboxes)
        appendHiddenInputsForDelete(".delete_h1_item_button", "h1");
        appendHiddenInputsForDelete(".delete_h2_item_button", "h2");

        // submit the form
        this.submit();
    });
});

$(document).ready(function () {
    $("button.notification-test").click(function (e) {
        e.stopPropagation(); // prevent Rails UJS click event
        e.preventDefault();

        let data = $("#new_garuda_notification")
            .serializeArray()
            .reduce(function (obj, item) {
                obj[item.name] = item.value;
                return obj;
            }, {});
        let title = data["garuda_notification[title]"];
        let body = data["garuda_notification[body]"];
        let path = data["garuda_notification[path]"];

        ActiveAdmin.ModalDialog("Send test notification to: ", {"user id": "number"}, function (inputs) {
            $.post({
                url: "/admin/notifications/send_test_notification",
                data: {user_id: inputs["user id"], title: title, body: body, path: path},
                dataType: "json",
                success: function (response) {
                    if (response["success"]) {
                        alert("Notification sent successfully");
                    } else {
                        alert(response["message"]);
                    }
                },
            });
        });
    });
});

$(document).ready(function () {
    if (window.location.pathname.includes("/poster_creatives/") && window.location.pathname.includes("/edit")) {
        const $jidField = $("#scheduled_notification_job");
        let previousStartTime = $("#creative_start_time").val();

        $("#creative_active_checkbox").change(function () {
            if ($jidField.length > 0 && !this.checked) {
                const confirmMessage =
                    "DM message scheduled | On 'inactive', DM notification will be stopped. To reschedule reach out to Tech Team\n" +
                    "Are you sure you want to continue?";
                if (!confirm(confirmMessage)) {
                    this.checked = true;
                }
            }
        });

        $("#creative_start_time").change(function () {
            let currentStartTime = $(this).val();
            const confirmMessage =
                "DM message scheduled | DM Notification will be rescheduled to the new start time\n" +
                "Are you sure you want to continue?";
            if (!confirm(confirmMessage)) {
                $(this).val(previousStartTime);
            } else {
                previousStartTime = currentStartTime;
            }
        });
    }
});

document.addEventListener("DOMContentLoaded", function () {
    function fetchSuggestions(field, language) {
        let query = field.value.trim();
        if (query.length > 1) {
            fetch(`/admin/roles/name_or_name_en_suggestion?query=${query}&language=${language}`)
                .then((response) => response.json())
                .then((suggestions) => {
                    const suggestionList = document.getElementById(field.id + "_suggestions");
                    if (!suggestions.length) {
                        suggestionList.innerHTML = "";
                    }
                    if (Array.isArray(suggestions)) {
                        suggestionList.innerHTML = "";
                        suggestions.forEach((term) => {
                            const item = document.createElement("li");
                            item.textContent = term;
                            item.addEventListener("click", () => {
                                field.value = term;
                                suggestionList.innerHTML = "";
                            });
                            suggestionList.appendChild(item);
                        });
                    }
                })
                .catch(() => {
                });
        } else {
            const suggestionList = document.getElementById(field.id + "_suggestions");
            if (suggestionList) {
                suggestionList.innerHTML = "";
            }
        }
    }

    function createSuggestionList(field) {
        const suggestionList = document.createElement("ul");
        suggestionList.id = field.id + "_suggestions";
        suggestionList.style.position = "absolute";
        suggestionList.style.listStyle = "none";
        suggestionList.style.padding = "0";
        suggestionList.style.zIndex = "1000";
        suggestionList.style.maxHeight = "200px";
        suggestionList.style.overflowY = "auto";
        suggestionList.style.background = "black";
        suggestionList.style.color = "white";
        const rect = field.getBoundingClientRect();
        suggestionList.style.top = `${rect.bottom + window.scrollY}px`;
        suggestionList.style.left = `${rect.left + window.scrollX}px`;
        field.parentNode.appendChild(suggestionList);
        return suggestionList;
    }

    const nameField = document.getElementById("role_name");
    const nameEnField = document.getElementById("role_name_en");
    if (nameField) {
        const nameSuggestionsList = createSuggestionList(nameField);
        nameField.addEventListener("input", function () {
            fetchSuggestions(nameField, "telugu");
        });
    }
    if (nameEnField) {
        const nameEnSuggestionsList = createSuggestionList(nameEnField);
        nameEnField.addEventListener("input", function () {
            fetchSuggestions(nameEnField, "english");
        });
    }
});

$(document).ready(function () {
    const $enableChannelCheckbox = $("#enable_channel_checkbox");
    const $noChannelTargets = $(".no_channel");
    const $enableFanPostersCheckbox = $("#enable_fan_posters_checkbox");
    const $noFanPostersTargets = $(".no_fan_posters");

    toggleChannelFields();
    toggleFanPosterFields();

    $enableChannelCheckbox.change(toggleChannelFields);
    $enableFanPostersCheckbox.change(toggleFanPosterFields);

    function toggleChannelFields() {
        if ($enableChannelCheckbox.is(":checked")) {
            $noChannelTargets.show();
        } else {
            $noChannelTargets.hide();
            $noChannelTargets.find('input[type="text"], select').val("");
            $noChannelTargets.find('input[type="checkbox"]').prop("checked", false);
            $noChannelTargets.find('input[type="checkbox"]').change();
        }
    }

    function toggleFanPosterFields() {
        if ($enableFanPostersCheckbox.is(":checked")) {
            $noFanPostersTargets.show();
        } else {
            $noFanPostersTargets.hide();
            $noFanPostersTargets.find("input, select").val("");
        }
    }
});

// To show or hide active and leader photos in circle page and show options based on type selected
$(document).ready(function () {
    // Selecting elements
    var $circleTypeSelect = $('[name="circle[circle_type]"]');
    var $levelSelect = $('select[data-toggle="dynamic_visibility"]');
    var $leaderPhotos = $(".leader_photos");
    var $activeInput = $(".active_input");

    // Function to fetch and update level options
    function fetchAndUpdateLevels(circleType, selectedLevel) {
        $.ajax({
            url: "/admin/circles/get_level_options",
            type: "GET",
            data: {circle_type: circleType},
            success: function (response) {
                $levelSelect.empty();
                var levels = response["levels"];

                levels.forEach(function (level) {
                    $levelSelect.append(
                        $("<option>", {
                            value: level,
                            text: level,
                            selected: selectedLevel === level,
                        })
                    );
                });

                // If no level is pre-selected, select the first one
                if (!selectedLevel || levels.indexOf(selectedLevel) === -1) {
                    $levelSelect.find("option:first").prop("selected", true);
                }

                // Update visibility after updating levels
                updateVisibility();
            },
            error: function () {
                console.error("Error fetching levels");
                $levelSelect.empty();
                $levelSelect.append(
                    $("<option>", {
                        value: "",
                        text: "Error loading levels",
                    })
                );
            },
        });
    }

    // Function to update visibility based on selected level
    function updateVisibility() {
        var value = $levelSelect.val();
        switch (value) {
            case "political_party":
                $leaderPhotos.show();
                $activeInput.hide(); // Ensure this is visible unless 'political_leader' is selected
                break;
            case "political_leader":
                $activeInput.show();
                $leaderPhotos.hide(); // Ensure this is visible unless 'political_party' is selected
                break;
            default:
                $leaderPhotos.hide();
                $activeInput.show();
                break;
        }
    }

    // Attach event listener for circle type change
    $circleTypeSelect.change(function () {
        var selectedLevel = $levelSelect.val();
        fetchAndUpdateLevels($(this).val(), selectedLevel);
    });

    // Attach event listener for level select change
    $levelSelect.change(updateVisibility);

    // Initial load
    var initialLevel = $levelSelect.val();
    fetchAndUpdateLevels($circleTypeSelect.val(), initialLevel);
    updateVisibility(); // Call on load to set initial visibility based on current selection
});

// $(document).ready(function () {
//     const $eventCheckbox = $('#has_event_checkbox');
//     const $paidCheckbox = $('#has_paid_checkbox');
//     const $sendDmMessage = $('.send_dm_message_field');
//     const $sendDmMsgNotification = $('.send_msg_notification_field');
//     const $dmTextMessage = $('.dm_text_message_field');
//     const $sendDmMessageCheckbox = $('.send_dm_message_field input[type=checkbox]');
//     const $sendDmMsgNotificationCheckbox = $('.send_msg_notification_field input[type=checkbox]');
//     const $dmTextMessageInput = $('.dm_text_message_field input[type=text]');
//     show_dm_fields();
//
//     $('#has_event_checkbox, #has_paid_checkbox').on('change', function () {
//         show_dm_fields();
//     });
//
//     $sendDmMessage.on('change', function () {
//         if ($sendDmMessageCheckbox.is(':checked')) {
//             $sendDmMsgNotification.show();
//             $dmTextMessage.show();
//         } else {
//             $sendDmMsgNotification.hide();
//             $sendDmMsgNotificationCheckbox.prop('checked', false);
//             $dmTextMessage.hide();
//             $dmTextMessageInput.val('');
//         }
//     });
//
//     function show_dm_fields() {
//         if ($eventCheckbox.is(':checked') || $paidCheckbox.is(':checked')) {
//             $sendDmMessage.hide();
//             $sendDmMsgNotification.hide();
//             $dmTextMessage.hide();
//             $sendDmMessageCheckbox.prop('checked', false).trigger('change');
//         } else {
//             $sendDmMessage.show();
//         }
//     }
// });

// for tagging on put getting approved and decline tags
$(document).ready(function () {
    $(".admin-tagging-select").on("select2:select select2:unselect", handleSelectInput);

    $(".approve-button").on("click", function () {
        const tagId = $(this).val();
        const tagApproveButton = $(`.approve-button[data-tag-id="${tagId}"]`);
        const tagDeclineButton = $(`.decline-button[data-tag-id="${tagId}"]`);
        const isApproveButton = tagApproveButton.hasClass("approve-button");
        const activeApproveButton = tagApproveButton.hasClass(activeClass);
        const inactiveApproveButton = tagApproveButton.hasClass(inactiveClass);
        const selectField = $(".admin-tagging-select");
        const selectFieldInput = selectField.val();

        // If an "Approve" button is clicked
        if (inactiveApproveButton === true || (isApproveButton === true && activeApproveButton === false)) {
            // No active button for this tag, activate the "Approve" button
            // Add tag to approved items and remove from declined items
            // update searchable select field
            tagApproveButton.removeClass(inactiveClass);
            tagApproveButton.addClass(activeClass);
            approvedItems.push(tagId);

            const index = declinedItems.indexOf(tagId);
            if (index !== -1) {
                declinedItems.splice(index, 1);
            }
            tagDeclineButton.removeClass(activeClass);
            tagDeclineButton.addClass(inactiveClass);

            selectFieldInput.push(tagId);
            selectField.val(selectFieldInput);
            selectField.trigger("change");
        } else if (activeApproveButton === true) {
            // An "Approve" button is already active, deactivate it
            // Remove tag from approved items and update searchable select field
            tagApproveButton.removeClass(activeClass);
            tagApproveButton.addClass(inactiveClass);

            const index = approvedItems.indexOf(tagId);
            if (index !== -1) {
                approvedItems.splice(index, 1);
            }

            const index2 = selectFieldInput.indexOf(tagId);
            if (index2 !== -1) {
                selectFieldInput.splice(index2, 1);
            }

            selectField.val(selectFieldInput);
            selectField.trigger("change");

            tagDeclineButton.removeClass(activeClass);
            tagDeclineButton.addClass(inactiveClass);
        }
    });

    $(".decline-button").on("click", function () {
        const tagId = $(this).val();
        const tagDeclineButton = $(`.decline-button[data-tag-id="${tagId}"]`);
        const tagApproveButton = $(`.approve-button[data-tag-id="${tagId}"]`);
        const isDeclineButton = tagDeclineButton.hasClass("decline-button");
        const activeDeclineButton = tagDeclineButton.hasClass(activeClass);
        const inactiveDeclineButton = tagDeclineButton.hasClass(inactiveClass);
        const selectField = $(".admin-tagging-select");
        const selectFieldInput = selectField.val();

        // If a "Decline" button is clicked
        if (inactiveDeclineButton === true || (isDeclineButton === true && activeDeclineButton === false)) {
            // No active button on decline, activate the "Decline" button and deactivate the "Approve" button
            // Add tag to declined items and remove from approved items
            // update searchable select field
            tagDeclineButton.removeClass(inactiveClass);
            tagDeclineButton.addClass(activeClass);
            declinedItems.push(tagId);

            const index = approvedItems.indexOf(tagId);
            if (index !== -1) {
                approvedItems.splice(index, 1);
            }

            const index2 = selectFieldInput.indexOf(tagId);
            if (index2 !== -1) {
                selectFieldInput.splice(index2, 1);
            }

            selectField.val(selectFieldInput);
            selectField.trigger("change");

            tagApproveButton.removeClass(activeClass);
            tagApproveButton.addClass(inactiveClass);
        } else if (activeDeclineButton === true) {
            // A "Decline" button is already active, deactivate it
            // Remove tag from declined items and update searchable select field
            tagDeclineButton.removeClass(activeClass);
            tagDeclineButton.addClass(inactiveClass);
            const index = declinedItems.indexOf(tagId);
            if (index !== -1) {
                declinedItems.splice(index, 1);
            }
            tagApproveButton.removeClass(activeClass);
            tagApproveButton.addClass(inactiveClass);
        }
    });

    function updateHiddenField() {
        $("#approved-declined-items").val(
            JSON.stringify({
                approved: approvedItems,
                declined: declinedItems,
            })
        );
    }

    function handleSelectInput(event) {
        const eventParams = event.params;
        const eventData = eventParams.data;
        const eventName = eventParams._type;
        const tagId = eventData.id;

        const ApproveButton = $(`.approve-button[data-tag-id="${tagId}"]`);
        const DeclineButton = $(`.decline-button[data-tag-id="${tagId}"]`);

        if (eventName === "select" && tagId !== "") {
            // If the "Select" option is selected, activate the "Approve" and deactivate the "Decline" button
            ApproveButton.removeClass(inactiveClass);
            ApproveButton.addClass(activeClass);
            if (!approvedItems.includes(tagId)) {
                approvedItems.push(tagId);
            }
            const index = declinedItems.indexOf(tagId);
            if (index !== -1) {
                declinedItems.splice(index, 1);
            }
            DeclineButton.removeClass(activeClass);
            DeclineButton.addClass(inactiveClass);
        } else if (eventName === "unselect" && tagId !== "") {
            // If the "Select" option is unselected, deactivate the "Approve" and activate the "Decline" button
            DeclineButton.removeClass(activeClass);
            DeclineButton.addClass(inactiveClass);
            const index = approvedItems.indexOf(tagId);
            if (index !== -1) {
                approvedItems.splice(index, 1);
            }
            ApproveButton.removeClass(activeClass);
            ApproveButton.addClass(inactiveClass);
        }
        updateHiddenField();
    }
});

$(document).ready(function () {
    function disableElementsWithIDs(prefix) {
        $(`[id^="${prefix}"]`).each(function () {
            $(this).attr("disabled", true);
            $(this).prop("checked", false);
        });
    }

    function enableElementsWithIDs(prefix) {
        $(`[id^="${prefix}"]`).each(function () {
            $(this).attr("disabled", false);
        });
    }

    let durationField = $("#duration_field_id");
    let durationFieldValue = durationField.val();
    let selectedPremiumFrameIdsCount = 0;
    let selectedStatusFrameIdsCount = 0;
    let amountField = $("#amount_field_id");

    //add text after class frame-label
    $(".frame-label").after(
        '<p class="note-text">(Images are only for frame reference. Use "Preview" for actual images)</p>'
    );

    //show a message to select duration field if it is empty
    if (durationFieldValue === "") {
        durationField.after(
            '<p class="hint-message" style="color: red; font-weight: bold;">Please select duration</p>'
        );
    }

    $(document).on(
        "input click",
        "#duration_field_id, .package-frame-checkbox," +
        " .frame-add-on-checkbox, .status-frame-checkbox " +
        ", #discount_amount_field_id, .activate-button, .preview-button ",
        async function () {
            //always disable neutral_flat_identity checkbox
            var label = $('label:contains("neutral_flat_identity")');

            // Check if a label element with the specified text was found
            if (label.length > 0) {
                // Do something with the label element or its associated input element
                var inputElement = label.find('input[type="checkbox"]');
                // Now you can access and manipulate the checkbox input element
                inputElement.prop({
                    checked: true,
                    disabled: true,
                });
            }

            //enable all checkboxes initially if duration field is not empty
            if (durationField.length && durationField.val() !== "") {
                durationField.siblings(".hint-message").remove();
                enableElementsWithIDs("user_poster_layout_premium_package_frame_ids_");
                enableElementsWithIDs("user_poster_layout_premium_frame_add_on_ids_");
                enableElementsWithIDs("user_poster_layout_status_frame_ids_");
            }
            let selectedPremiumFrameIds = [];
            let selectedPremiumFrameAddOnIds = [];
            let selectedStatusFrameIds = [];
            durationFieldValue = durationField.val();

            //premium-frame-addon-label class need to be hide always
            $(".premium-frame-add-on-section").hide();
            if (durationField.length && durationField.val() !== "") {
                // Loop through all the selected checkboxes
                $(".package-frame-checkbox:checked").each(function () {
                    selectedPremiumFrameIds.push($(this).val());
                    const frameId = $(this).val();
                    var frameAddOnCheckbox = $("#user_poster_layout_premium_frame_add_on_ids_" + frameId);
                    frameAddOnCheckbox.prop({
                        checked: false,
                        disabled: true,
                    });
                });

                $(".frame-add-on-checkbox:checked").each(function () {
                    selectedPremiumFrameAddOnIds.push($(this).val());
                    const frameId = $(this).val();

                    var packageFrameCheckbox = $("#user_poster_layout_premium_package_frame_ids_" + frameId);
                    packageFrameCheckbox.prop({
                        checked: false,
                        disabled: true,
                    });
                });

                // Now 'selectedPremiumFrameIds' contains an array of selected frame IDs
                selectedPremiumFrameIdsCount = selectedPremiumFrameIds.length;

                // Loop through all the selected checkboxes
                $(".status-frame-checkbox:checked").each(function () {
                    selectedStatusFrameIds.push($(this).val());
                });

                selectedStatusFrameIdsCount = selectedStatusFrameIds.length;
                // Now 'selectedStatusFrameIds' contains an array of selected frame IDs
                if (selectedPremiumFrameIdsCount >= 5) {
                    $('[id^="user_poster_layout_premium_package_frame_ids_"]:not(:checked)').each(function () {
                        $(this).attr("disabled", true);
                        //remove the message to select 5 frames
                        $(".hint-message").remove();
                        $(".activate-button").attr("disabled", false);
                        $(".preview-button").attr("disabled", false);
                    });
                    await getAmountByDuration(durationFieldValue, selectedPremiumFrameAddOnIds, selectedStatusFrameIds);
                    await showMaxDiscountAmount(durationFieldValue);
                    calculatePayableAmount();
                } else {
                    //remove hint message if any
                    $(".hint-message").remove();
                    $(".premium-package-label").after(
                        '<p class="hint-message" style="color: red; font-weight: bold;">Please select 5 frames</p>'
                    );

                    $('[id^="user_poster_layout_premium_package_frame_ids_"]:not(:checked)').each(function () {
                        $(this).attr("disabled", false);
                        //disable save for preview and submit for approval button
                        $(".activate-button").attr("disabled", true);
                        $(".preview-button").attr("disabled", true);
                    });

                    amountField.val("");
                    let discountField = $("#discount_amount_field_id");
                    discountField.val("");
                    discountField.siblings("#max-discount-hint-message").remove();
                    $("#payable_amount_field_id").val("");
                }
            }
            // disable all checkboxes if duration field is empty
            else if (durationField.length && durationField.val() === "") {
                disableElementsWithIDs("user_poster_layout_premium_package_frame_ids_");
                disableElementsWithIDs("user_poster_layout_premium_frame_add_on_ids_");
                disableElementsWithIDs("user_poster_layout_status_frame_ids_");
            }
        }
    );

    //disable all checkboxes initially if duration field is empty
    if (durationField.length && durationField.val() === "") {
        disableElementsWithIDs("user_poster_layout_premium_package_frame_ids_");
        disableElementsWithIDs("user_poster_layout_premium_frame_add_on_ids_");
        disableElementsWithIDs("user_poster_layout_status_frame_ids_");
    }

    async function getAmountByDuration(durationValue, selectedPremiumFrameAddOnIds, selectedStatusFrameIds) {
        // url: '/admin/user_poster_layouts/' + $('#your_resource_id').val() + '/get_amount_by_duration',
        try {
            await $.ajax({
                url: "/admin/user_poster_layouts/new/get_amount_by_duration",
                data: {
                    duration: durationValue,
                    selected_premium_frame_addon_ids: selectedPremiumFrameAddOnIds,
                    selected_status_frame_ids: selectedStatusFrameIds,
                },
                dataType: "json",
                success: function (response) {
                    if (response["success"]) {
                        amountField.siblings(".hint-message").remove();
                        amountField.val(response["amount"]);

                        $("#hidden_amount_field_id").val(response["amount"]);
                    } else {
                        // I want to show customized message here as hint under amount field.

                        // Remove any existing hint message
                        amountField.siblings(".hint-message").remove();

                        // Add a hint message as a sibling after the amount field
                        amountField.after(
                            '<p class="hint-message" style="color: red; font-weight: bold;">Something wrong with the chosen frame and duration</p>'
                        );

                        // Clear the amount field value
                        amountField.val("");
                    }
                },
            });
            return amountField.val();
        } catch (e) {
            console.log(e);
            amountField.val("");
            return amountField.val();
        }
    }

    async function showMaxDiscountAmount(durationValue) {
        let discountField = $("#discount_amount_field_id");
        let maintenanceField = $("#maintenance_amount_field_id");

        let maintenance_price;

        if (durationValue > 0) {
            try {
                await $.ajax({
                    url: "/admin/user_poster_layouts/new/get_maintenance_price",
                    data: {duration: durationValue},
                    dataType: "json",
                    success: function (response) {
                        if (response["success"]) {
                            maintenance_price = response["maintenance_price"];
                            maintenanceField.val(maintenance_price);
                            discountField.siblings("#max-discount-hint-message").remove();
                            discountField.after(
                                '<p id="max-discount-hint-message" style="color: darkgoldenrod; font-weight: bold; font-size: 14px;">Maximum discount amount : ' +
                                maintenance_price +
                                "</p>"
                            );
                        }
                    },
                });
            } catch (e) {
                console.log(e);
                discountField.val("");
                return discountField.val();
            }
        }
    }

    function calculatePayableAmount() {
        let discountField = $("#discount_amount_field_id");
        let discountAmount = discountField.val();

        let totalAmountField = $("#amount_field_id");
        let totalAmount = totalAmountField.val();

        let payableField = $("#payable_amount_field_id");

        let maintenanceField = $("#maintenance_amount_field_id");
        let maintenanceAmount = maintenanceField.val();

        let discountHint = $("#max-discount-hint-message");

        let discount = discountAmount ?? 0;
        let amount = totalAmount ?? 0;
        let maintenance = maintenanceAmount ?? 0;
        let payableAmount = amount - discount;

        discount = parseInt(discount);
        maintenance = parseInt(maintenance);

        discountHint.siblings(".hint-message").remove();
        payableField.val(payableAmount);

        if (discount > maintenance) {
            discountHint.after(
                '<p class="hint-message" style="color: red; font-weight: bold; font-size: medium;">Discount amount cannot be greater than max discount amount.</p>'
            );
        } else {
            discountHint.after(
                '<p class="hint-message" style="color: green; font-weight: bold; font-size: medium;">Payable Amount: ' +
                payableAmount +
                "</p>"
            );
        }
    }
});

// Function to initialize Select2 and reorder selected options
function initializeSelect2() {
    var $select = $(".role-display-name-select").select2();

    $select.on("select2:select", function (e) {
        var selectedOption = $(e.params.data.element);
        selectedOption.detach();
        $(this).append(selectedOption);
        $(this).trigger("change");
    });

    return $select;
}

// Function to add or remove options based on role_has_purview
function handleRoleHasPurview() {
    var $select = initializeSelect2();

    document.querySelectorAll(".role_has_purview").forEach(function (element) {
        element.addEventListener("change", function () {
            var isChecked = this.checked;

            if (isChecked) {
                addPurviewOptions($select);
            } else {
                removePurviewOptions($select);
            }
        });
    });

    if (!$(".role_has_purview").is(":checked")) {
        removePurviewOptions($select);
    }
}

// Function to add purview options
function addPurviewOptions($select) {
    var purview_circle_option = new Option("purview_circle", "purview_circle", false, false);
    $select.append(purview_circle_option);
    var purview_circle_suffix_option = new Option("purview_circle_suffix", "purview_circle_suffix", false, false);
    $select.append(purview_circle_suffix_option);
}

// Function to remove purview options
function removePurviewOptions($select) {
    $select.find('option[value="purview_circle"]').remove();
    $select.find('option[value="purview_circle_suffix"]').remove();
}

function handleRoleHasBadge() {
    var $select = initializeSelect2();

    document.querySelectorAll(".role_has_badge").forEach(function (element) {
        element.addEventListener("change", function () {
            var isChecked = this.checked;

            if (isChecked) {
                addBadgeOptions($select);
            } else {
                removeBadgeOptions($select);
            }
        });
    });

    if (!$(".role_has_badge").is(":checked")) {
        removeBadgeOptions($select);
    }
}

// Function to add badge options
function addBadgeOptions($select) {
    var badge_option = new Option("badge_circle_text", "badge_circle_text", false, false);
    $select.append(badge_option);
}

// Function to remove badge options
function removeBadgeOptions($select) {
    $select.find('option[value="badge_circle_text"]').remove();
}

// Execute the code when the document is ready
$(document).ready(function () {
    handleRoleHasPurview();
    handleRoleHasBadge();
});

$(document).ready(function () {
    var user_role_role_id = $("#user_role_role_id");
    var userRoleSelect = $(".user-role-display-name-select").select2();
    var parentCircle = $("#user_role_parent_circle_id");
    var freeText = $("#user_role_free_text");

    // Move the selected option to the end of the select element's list to maintain the order of selection
    userRoleSelect.on("select2:select", function (e) {
        // Get the selected option and detach it from its current position
        var selectedOption = $(e.params.data.element);
        selectedOption.detach();

        // Append it to the end of the select element's list
        $(this).append(selectedOption);

        // Update the select2 display
        $(this).trigger("change");
    });

    function addPurviewFields() {
        // Add the purview_circle and purview_circle_suffix option
        if (userRoleSelect.find('option[value="purview_circle"]').length === 0) {
            var purview_circle_option = new Option("purview_circle", "purview_circle", false, false);
            userRoleSelect.append(purview_circle_option);
        }
        if (userRoleSelect.find('option[value="purview_circle_suffix"]').length === 0) {
            var purview_circle_suffix_option = new Option(
                "purview_circle_suffix",
                "purview_circle_suffix",
                false,
                false
            );
            userRoleSelect.append(purview_circle_suffix_option);
        }
    }

    function removePurviewFields() {
        userRoleSelect.find('option[value="purview_circle"]').remove();
        userRoleSelect.find('option[value="purview_circle_suffix"]').remove();
    }

    function addBadgeFields() {
        // Add the badge_circle_text option
        if (userRoleSelect.find('option[value="badge_circle_text"]').length === 0) {
            var badge_option = new Option("badge_circle_text", "badge_circle_text", false, false);
            userRoleSelect.append(badge_option);
        }
    }

    function removeBadgeFields() {
        userRoleSelect.find('option[value="badge_circle_text"]').remove();
    }

    function disableParentCircle() {
        parentCircle.prop("disabled", true);
        parentCircle.val("");
    }

    function enableParentCircle() {
        parentCircle.prop("disabled", false);
    }

    function disableFreeText() {
        freeText.prop("disabled", true);
        freeText.val("");
    }

    function enableFreeText() {
        freeText.prop("disabled", false);
    }

    function handleRoleChange() {
        const selectedRoleId = user_role_role_id.val();
        if (selectedRoleId !== "") {
            // Make an AJAX request to fetch the associated role's data
            $.ajax({
                url: "/admin/user_roles/new/role_data_for_user_role_form",
                method: "GET",
                dataType: "json",
                data: {role_id: selectedRoleId},
                success: function (response) {
                    if (response["role_has_purview"]) {
                        addPurviewFields();
                    } else {
                        removePurviewFields();
                    }
                    if (response["role_has_badge"]) {
                        addBadgeFields();
                    } else {
                        removeBadgeFields();
                    }
                    if (response["role_has_parent_circle"]) {
                        disableParentCircle();
                    } else {
                        enableParentCircle();
                    }
                    if (response["role_has_free_text"]) {
                        enableFreeText();
                    } else {
                        disableFreeText();
                    }
                },
                error: function () {
                    console.log("Error occurred while checking role.");
                },
            });
        } else {
            removePurviewFields();
            removeBadgeFields();
            enableParentCircle();
            disableFreeText();
        }
    }

    // Add an event listener for the change event on user_role_role_id
    user_role_role_id.on("change", handleRoleChange);

    // Call the function initially if user_role_role_id has a pre-selected value
    if (user_role_role_id.val() !== "") {
        handleRoleChange();
    }
});

$(document).ready(function () {
    var $parentCircleId = $(".role_parent_circle_id");
    var $parentCircleLevel = $(".role_parent_circle_level");
    var $parentCircleLevelSelect = $("#role_parent_circle_level_input");

    // Function to toggle the visibility of parent_circle_level
    function toggleParentCircleLevelVisibility() {
        if ($parentCircleId.val() !== "") {
            $parentCircleLevelSelect.hide();
        } else {
            $parentCircleLevelSelect.show();
        }
    }

    // Function to toggle the disabled state of parent_circle_id
    function toggleParentCircleIdDisabled() {
        if ($parentCircleLevel.val() !== "") {
            $parentCircleId.prop("disabled", true);
        } else {
            $parentCircleId.prop("disabled", false);
        }
    }

    // Initially, set the visibility and disabled state based on the values
    toggleParentCircleLevelVisibility();
    toggleParentCircleIdDisabled();

    // on change of parent circle level, toggle the state of parent circle id
    $parentCircleId.on("change", function () {
        toggleParentCircleLevelVisibility();
        toggleParentCircleIdDisabled();
    });

    $parentCircleLevel.on("change", function () {
        toggleParentCircleIdDisabled();
    });
});

$(document).ready(function () {
    function syncHiddenFields() {
        //if role_has_purview checked then it's and hidden_role_has_purview value is 1 elso 0
        if ($(".role_has_purview").is(":checked")) {
            $("#hidden_role_has_purview").val(1);
        } else {
            $("#hidden_role_has_purview").val(0);
        }
        $("#hidden_role_parent_circle_level").val($(".role_parent_circle_level").val());
        $("#hidden_role_purview_level").val($(".role_purview_level").val());
    }

    // Sync hidden fields on page load
    syncHiddenFields();

    // Sync hidden fields when inputs change
    $(".role_parent_circle_level, .role_has_purview, .role_purview_level").on("change", function () {
        syncHiddenFields();
    });

    $(".admin_roles").on("submit", function () {
        // Enable hidden inputs before submission
        $("#hidden_role_parent_circle_level").prop("disabled", false);
        $("#hidden_role_has_purview").prop("disabled", false);
        $("#hidden_role_purview_level").prop("disabled", false);
    });
});

$(document).ready(function () {
    $(".user_roles_purview_circle_id").select2();
    $(".user_roles_parent_circle_id").select2();
    $(".role_parent_circle_id").select2();
    const $canClearSelectionElements = $(".can_clear_selection");
    $canClearSelectionElements.each(function (index, $element) {
        // var $select = $($element).select2();
        $("<button/>", {
            text: "Clear Selection",
            class: "clear-button",
            click: function (e) {
                e.preventDefault();
                $($element).val(null).trigger("change");
            },
        }).insertAfter($($element).next(".select2"));
    });
});

$(document).ready(function () {
    var $verificationStatus = $("#verification_status");
    var $isLetterPendingWrapper = $("#is_letter_pending");
    var $proofLinkWrapper = $("#proof_link");
    var $fileWrapper = $("#file");

    function toggleIsLetterPending() {
        if ($verificationStatus.val() != "verified") {
            $isLetterPendingWrapper.show();
        } else {
            $isLetterPendingWrapper.hide();
        }
    }

    function toggleProofLinkFile() {
        if ($verificationStatus.val() == "verified") {
            $proofLinkWrapper.show();
            $fileWrapper.show();
        } else {
            $proofLinkWrapper.hide();
            $fileWrapper.hide();
        }
    }

    toggleIsLetterPending();
    toggleProofLinkFile();

    $verificationStatus.on("change", function () {
        toggleIsLetterPending();
        toggleProofLinkFile();
    });
});

$(document).ready(function () {
    $(".copy-link-button").on("click", function () {
        const $link = $(this).data("link");
        if (!navigator.clipboard) {
            alert("Clipboard API not supported");
            return;
        }
        navigator.clipboard
            .writeText($link)
            .then(function () {
                alert("Copied the link");
            })
            .catch(function (err) {
                alert("Error in copying the link. Try copying manually.");
            });
    });
});

//function to disable specific role fields
function disableSpecificRoleFields() {
    $(".role_parent_circle_level").prop("disabled", true);
    $(".role_has_purview").prop("disabled", true);
    $(".role_purview_level").prop("disabled", true);
}

document.addEventListener("DOMContentLoaded", function () {
    // Check if it's the roles edit page and if it's the edit page then check if the role has user roles
    if (window.location.href.includes("/admin/roles/") && window.location.href.includes("/edit")) {
        var $roleId = $("#role_id").val();
        $.ajax({
            url: "/admin/roles/edit/check_role_has_user_role",
            method: "GET",
            dataType: "json",
            data: {role_id: $roleId},
            success: function (response) {
                // If the role has user roles, disable the specific fields to not allow editing
                if (response["role_has_user_role"]) {
                    disableSpecificRoleFields();
                }
            },
            error: function () {
                console.log("Error occurred while checking role has user roles.");
            },
        });
    }
});

document.addEventListener("DOMContentLoaded", function () {
    window.openMultipleTabs = function (url1, url2) {
        window.open(url1, "_blank");
        window.open(url2, "_blank");
    };
});

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".filter_role_ids_field").forEach(function (field) {
        var preselectedRoles = field.getAttribute("data-preselected-roles");
        if (preselectedRoles) {
            // Wait for AJAX content to load, then set the selected values
            setTimeout(function () {
                preselectedRoles = JSON.parse(preselectedRoles);
                preselectedRoles.forEach(function (role) {
                    var option = new Option(role.name, role.id, true, true);
                    field.append(option);
                });
                // Trigger change event if using a plugin like Select2
                $(field).trigger("change");
            }, 1000); // Adjust timeout as necessary
        }
    });

    document.querySelectorAll(".filter_location_circle_ids_field").forEach(function (field) {
        var preselectedLocations = field.getAttribute("data-preselected-circles");
        if (preselectedLocations) {
            // Wait for AJAX content to load, then set the selected values
            setTimeout(function () {
                preselectedLocations = JSON.parse(preselectedLocations);
                preselectedLocations.forEach(function (circle) {
                    var option = new Option(circle.name, circle.id, true, true);
                    field.append(option);
                });

                // Trigger change event if using a plugin like Select2
                $(field).trigger("change");
            }, 1000); // Adjust timeout as necessary
        }
    });

    document.querySelectorAll(".filter_grade_levels_field").forEach(function (field) {
        var preselectedGradeLevels = field.getAttribute("data-preselected-grade-levels");
        if (preselectedGradeLevels) {
            // Wait for AJAX content to load, then set the selected values
            setTimeout(function () {
                preselectedGradeLevels = JSON.parse(preselectedGradeLevels);
                preselectedGradeLevels.forEach(function (grade_level_row) {
                    var option = new Option(grade_level_row.name, grade_level_row.id, true, true);
                    field.append(option);
                });

                // Trigger change event if using a plugin like Select2
                $(field).trigger("change");
            }, 1000); // Adjust timeout as necessary
        }
    });
});

document.addEventListener("DOMContentLoaded", function () {
    var extendTrialLink = document.getElementById("extend_trial_link");

    // Check if the extendTrialLink exists before adding the event listener
    if (extendTrialLink) {
        extendTrialLink.addEventListener("click", function (event) {
            event.preventDefault();
            var extendPath = this.getAttribute("data-extend-path");
            var numberOfDays = prompt(
                "Select the number of days for trial extension:\n\n7 - 7 days\n15 - 15 days\n30 - 30 days"
            );
            if (numberOfDays !== null && numberOfDays !== "") {
                numberOfDays = parseInt(numberOfDays); // Convert the input to a number
                if (numberOfDays === 7 || numberOfDays === 15 || numberOfDays === 30) {
                    window.location.href = extendPath + "?number_of_days=" + numberOfDays;
                } else {
                    alert("Invalid input. Please select either 7 or 15 or 30 days.");
                }
            }
        });
    }
});

$(document).ready(function () {
    var $user_id = $("#entity_id_input");
    var $customRoleNameField = $("#user_poster_layout_custom_role_name");

    // Call the function initially if user_id has a pre-selected value
    if ($user_id.val() !== "") {
        handleCustomRoleNameField();
    }

    $user_id.on("change", function () {
        handleCustomRoleNameField();
    });

    function handleCustomRoleNameField() {
        if ($user_id.val() !== "") {
            // Make an AJAX request to fetch the associated role's data
            $.ajax({
                url: "/admin/user_poster_layouts/new/user_has_badge",
                method: "GET",
                dataType: "json",
                data: {user_id: $user_id.val()},
                success: function (response) {
                    if (response["user_has_badge"]) {
                        $(".custom_role_name").show();
                    } else {
                        $(".custom_role_name").hide();
                    }
                },
                error: function () {
                    console.log("Error occurred while checking user role.");
                },
            });
        } else {
            $(".custom_role_name").hide();
            $customRoleNameField.style.display = "none";
        }
    }
});

document.addEventListener("DOMContentLoaded", function () {
    const durationInput = document.getElementById("duration_in_months_input");
    const maxDiscountDisplay = document.getElementById("max_discount_amount_display");
    const totalAmountField = document.getElementById("total_amount_input");
    const submitButtonContainer = document.getElementById("order_submit_action");
    const hiddenUserId = document.getElementById("hidden_order_user_id_input");
    const discountAmountField = document.getElementById("discount_amount_input");
    const payableAmountField = document.getElementById("payable_amount_input");
    const hiddenPayableAmountField = document.getElementById("hidden_payable_amount_input");
    const hiddenTotalAmountField = document.getElementById("hidden_total_amount_input");
    let submitButton;

    if (submitButtonContainer) {
        submitButton = submitButtonContainer.querySelector('input[type="submit"]');
    }

    function getAmountDataOfOpenOrder(duration) {
        if (hiddenUserId) {
            $.ajax({
                url: "/admin/orders/new/get_amount_data_of_open_order",
                data: {duration: duration, user_id: hiddenUserId.value},
                dataType: "json",
                success: function (response) {
                    if (response.success) {
                        if (maxDiscountDisplay) {
                            maxDiscountDisplay.textContent = `Max Discount Amount: ₹${response.maintenance_price}`;
                        }
                        if (totalAmountField) {
                            totalAmountField.value = response.amount;
                        }
                        if (hiddenTotalAmountField) {
                            hiddenTotalAmountField.value = response.amount;
                        }
                        calculatePayableAmount();
                        showExceedMaxDiscountField();
                        enableSubmitButton();
                    }
                },
            });
        }
    }

    if (durationInput) {
        durationInput.addEventListener("input", function () {
            if (maxDiscountDisplay) {
                maxDiscountDisplay.textContent = "Max Discount Amount: ";
            }
            const duration = durationInput.value;
            if (duration > 0) {
                getAmountDataOfOpenOrder(duration);
            }
        });

        if (durationInput.value > 0 && durationInput.value !== "") {
            getAmountDataOfOpenOrder(durationInput.value);
        } else {
            if (maxDiscountDisplay) {
                maxDiscountDisplay.textContent = "Max Discount Amount: ";
            }
            if (submitButton) {
                submitButton.disabled = true;
            }
        }
    }

    if (discountAmountField) {
        discountAmountField.addEventListener("input", function () {
            calculatePayableAmount();
            enableSubmitButton();
            showExceedMaxDiscountField();
        });
    }

    function enableSubmitButton() {
        const maxDiscount = parseInt(maxDiscountDisplay ? maxDiscountDisplay.textContent.split("₹")[1] : 0) || 0;
        if (durationInput && discountAmountField && submitButton) {
            if (durationInput.value > 0 && discountAmountField.value <= maxDiscount) {
                submitButton.disabled = false;
            } else {
                submitButton.disabled = true;
            }
        }
    }

    function calculatePayableAmount() {
        if (discountAmountField && totalAmountField && payableAmountField && hiddenPayableAmountField) {
            const discountAmount = discountAmountField.value;
            const totalAmount = totalAmountField.value;
            payableAmountField.value = totalAmount - discountAmount;
            hiddenPayableAmountField.value = payableAmountField.value;
            if (payableAmountField.value < 0) {
                payableAmountField.style.color = "red";
            } else {
                payableAmountField.style.color = "white";
            }
        }
    }

    function showExceedMaxDiscountField() {
        const maxDiscount = parseInt(maxDiscountDisplay ? maxDiscountDisplay.textContent.split("₹")[1] : 0) || 0;
        const discountAmount = parseInt(discountAmountField.value) || 0;
        const exceedMaxDiscountField = document.getElementById("exceed_max_discount_field");
        if (discountAmount > maxDiscount) {
            if (!exceedMaxDiscountField) {
                const fieldContainer = document.createElement("div");
                fieldContainer.id = "exceed_max_discount_field";
                fieldContainer.innerHTML = `
                <p id="exceed_max_discount_amount" style="color: red; margin-left: 20%;">Discount is greater than Max discount</p>
            `;
                discountAmountField.parentNode.insertBefore(fieldContainer, discountAmountField.nextSibling);
            } else {
                const exceedMaxDiscountAmountField = document.getElementById("exceed_max_discount_amount");
                exceedMaxDiscountAmountField.textContent = `Discount is greater than Max discount`;
            }
        } else {
            if (exceedMaxDiscountField) {
                exceedMaxDiscountField.parentNode.removeChild(exceedMaxDiscountField);
            }
        }
    }
});

$(document).ready(function () {
    $(document).on("click", ".update-button", function (e) {
        e.preventDefault();
        var constituencyId = $(this).data("constituency_id");
        var type = $(this).data("type");

        let get_url;
        let set_url;
        if (type === "parliament") {
            get_url = "/admin/parliament/get_constituency_data";
            set_url = "/admin/parliament/update_data";
        } else {
            get_url = "/admin/assembly/get_constituency_data";
            set_url = "/admin/assembly/update_data";
        }

        $.ajax({
            url: get_url,
            method: "GET",
            data: {
                constituency_id: constituencyId,
            },
            success: function (response) {
                let candidates = response["candidates"];
                let candidatesCircleIds = candidates.map((candidate) => candidate[0]);
                let candidatesNames = candidates.map((candidate) => candidate[1]);
                let constituencyName = response["constituency_name"];
                let lastUpdatedData = response["last_updated_data"];
                let selectedCandidateName;
                let selectedStatus;
                let selectedMajorityVotes;
                let createdBadgeAndNotified;

                if (lastUpdatedData) {
                    selectedCandidateName = lastUpdatedData["candidate_name"];
                    selectedStatus = lastUpdatedData["status"];
                    selectedMajorityVotes = lastUpdatedData["majority_votes"];
                    createdBadgeAndNotified = lastUpdatedData["create_badge_and_notify"];
                }

                ActiveAdmin.ModalDialog(
                    "Update Result for: " + constituencyName,
                    {
                        Candidate: candidatesNames,
                        Status: ["", "Lead", "Won"],
                        "Majority Votes": "number",
                        "Create badge and Notify": "checkbox",
                    },
                    function (inputs) {
                        if (selectedStatus === "Won") {
                            inputs["hiddenCandidate"] = selectedCandidateName;
                            inputs["hiddenStatus"] = selectedStatus;
                        }

                        let candidateCircleId =
                            candidatesCircleIds[
                                candidatesNames.indexOf(inputs["Candidate"] || inputs["hiddenCandidate"])
                                ];

                        $.post({
                            url: set_url,
                            data: {
                                authenticity_token: $('meta[name="csrf-token"]').attr("content"),
                                candidate_circle_id: candidateCircleId,
                                status: inputs["Status"] || inputs["hiddenStatus"],
                                majority_votes: inputs["Majority Votes"],
                                create_badge_and_notify: inputs["Create badge and Notify"] === "on" ? "true" : "false",
                                id: constituencyId,
                            },
                            dataType: "json",
                            success: function (response) {
                                if (response.success) {
                                    location.reload();
                                } else {
                                    alert(response.message);
                                }
                            },
                        });
                    }
                );
                // Pre-select the candidate and status after the dialog is created
                setTimeout(function () {
                    let candidateSelect = $('select[name="Candidate"]');
                    let statusSelect = $('select[name="Status"]');
                    let majorityVotesInput = $('input[name="Majority Votes"]');
                    let createBadgeAndNotifyCheckbox = $('input[name="Create badge and Notify"]');

                    candidateSelect.val(selectedCandidateName).trigger("change");
                    statusSelect.val(selectedStatus).trigger("change");
                    majorityVotesInput.val(selectedMajorityVotes);
                    createBadgeAndNotifyCheckbox.prop("checked", createdBadgeAndNotified);

                    // If the status is 'Won', disable both the candidate and status fields
                    if (selectedStatus === "Won") {
                        candidateSelect.prop("disabled", true);
                        statusSelect.prop("disabled", true);
                    }

                    let previousStatus = selectedStatus;

                    statusSelect.on("change", function () {
                        let newStatus = $(this).val();
                        if (newStatus === "Won") {
                            if (!confirm("Are you sure want to set the status to WON?")) {
                                $(this).val(previousStatus).trigger("change");
                            } else {
                                previousStatus = newStatus;
                            }
                        } else {
                            previousStatus = newStatus;
                        }
                    });
                }, 100);
            },
            error: function () {
                alert("Error occurred while fetching the data.");
            },
        });
    });
});

$(document).ready(function () {
    // Call on page load in case there are no requested circles
    updateButtons();

    // Add a MutationObserver to watch for changes to the requested circles container
    const requestedCirclesObserver = new MutationObserver(function (mutations) {
        // Check if the mutations affect the requested circles hidden inputs
        const shouldUpdate = mutations.some((mutation) => {
            return (
                $(mutation.target).attr("id") === "requested_circles_hidden_inputs" ||
                $(mutation.target).closest("#requested_circles_hidden_inputs").length > 0
            );
        });

        if (shouldUpdate) {
            // Update buttons after a short delay to allow for any other changes
            setTimeout(updateButtons, 100);
        }
    });

    // Start observing the requested circles hidden inputs container
    if ($("#requested_circles_hidden_inputs").length > 0) {
        requestedCirclesObserver.observe($("#requested_circles_hidden_inputs")[0], {
            childList: true,
            subtree: true,
            attributes: true,
        });
    }

    $(".existing-circle-select, .existing-circle-change-select").each(function () {
        var $select = $(this);
        // Initialize Select2 on the select elements (existing circles, requested circles, etc.)
        $select.select2({
            placeholder: "Search existing circles",
            minimumInputLength: 2,
            ajax: {
                url: "/admin/user_poster_layouts/get_es_circles",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        term: params.term,
                        entity_id: $select.data("layoutUserId"),
                    };
                },
                processResults: function (data) {
                    // Check if data is not defined or empty
                    if (!data) {
                        return {
                            results: [
                                {
                                    id: "no-circles-found",
                                    text: "No circles found",
                                },
                            ],
                        };
                    }

                    // Proceed with processing the valid data
                    return {
                        results: data.map(function (item) {
                            var text = item.name;
                            if (item.name_en) {
                                text += " (" + item.name_en + ")";
                            }
                            if (item.party_short_name) {
                                text += " – " + item.party_short_name;
                            }
                            return {id: item.id, text: text};
                        }),
                    };
                },
                cache: true,
            },
        });

        $select.on("select2:opening", function (e) {
            if ($select.data("selectedCircleId")) {
                // Clear the value so that the same selection can trigger again.
                $select.val(null).trigger("change");
                $select.removeData("selectedCircleId");
                // Also clear the hidden input associated with this requested circle.
                var reqCircleId = $select.data("reqCircleId");
                $('input[name="selected_circles[' + reqCircleId + ']"]').remove();
                // Clear the selected image container so that the photo is removed as well
                $("#selected_image_" + reqCircleId).empty();
                //toggle the submit button state after clearing the selected circle
                updateButtons();
            }
        });

        // When a circle is selected, handle the event
        $select.on("select2:select", function (e) {
            // Use the consistent key: here we assume the data attribute is "reqCircleId"
            var reqCircleId = $select.data("reqCircleId");
            // For requested circles, show the photo upload section if available
            if (reqCircleId) {
                $("#circle_photo_upload_" + reqCircleId).show();
            }
            // Save the selected circle id for later use
            $select.data("selectedCircleId", e.params.data.id);
            var circleId = $select.data("selectedCircleId");

            // If this select is for reassigning a requested circle, open the modal for photo selection
            if (reqCircleId) {
                $.ajax({
                    url: "/admin/user_poster_layouts/get_circle_poster_photo_urls",
                    method: "GET",
                    data: {circle_id: circleId},
                    success: function (photoList) {
                        // Combine the returned arrays into an array of photo objects
                        var photos = [];
                        if (
                            photoList.circlePhotoUrls &&
                            photoList.circlePhotoIds &&
                            photoList.circlePhotoUrls.length === photoList.circlePhotoIds.length
                        ) {
                            for (var i = 0; i < photoList.circlePhotoUrls.length; i++) {
                                photos.push({
                                    id: photoList.circlePhotoIds[i],
                                    url: photoList.circlePhotoUrls[i],
                                });
                            }
                        } else {
                            console.error("Invalid photoList format", photoList);
                        }

                        // Build HTML content for the modal with radio buttons and image previews.
                        // Each radio input has a data-url attribute for its image URL.
                        var modalContent =
                            '<div class="poster-photo-options" style="display: flex; flex-wrap: wrap; gap: 8px;">';
                        photos.forEach(function (photo) {
                            modalContent +=
                                '<label style="display: inline-flex; align-items: center;">' +
                                '<input type="radio" name="circle_photo_choice_' +
                                reqCircleId +
                                '" value="' +
                                photo.id +
                                '" data-url="' +
                                photo.url +
                                '" style="margin-right:5px;" />' +
                                '<img src="' +
                                photo.url +
                                '" alt="Photo ' +
                                photo.id +
                                '" style="max-width:100px; vertical-align:middle;" />' +
                                "</label>";
                        });
                        modalContent += "</div>";

                        // Insert the custom HTML content into ActiveAdmins modal form container
                        $("#dialog_confirm").html(modalContent);

                        // Compute width based on the number of photos
                        var count = photos.length;
                        var colCount;
                        if (count <= 6) {
                            colCount = 3;
                        } else if (count <= 10) {
                            colCount = 5;
                        } else {
                            colCount = 6;
                        }

                        var photoWidth = 140; // fixed width per photo (adjust as needed)
                        var gap = 10; // gap between photos (should match your modalContent gap)
                        var extraPadding = 50; // additional space for margins/padding
                        var columns = Math.min(count, colCount);
                        var newWidth = columns * photoWidth + (columns - 1) * gap + extraPadding;
                        newWidth = Math.max(newWidth, 300);

                        setTimeout(function () {
                            // Use jQuery UI's widget method to get the dialog widget element
                            var $dialog = $("#dialog_confirm").dialog("widget");
                            if ($dialog && $dialog.length > 0) {
                                $dialog.css("width", newWidth + "px");
                            } else {
                                console.error("Dialog widget not found");
                            }
                        }, 300);

                        // Open the modal dialog to let the user choose a photo
                        ActiveAdmin.ModalDialog("Choose Poster Photo", {}, function (inputs) {
                            // Use the modal container (#dialog_confirm) to get the checked radio button
                            var $checked = $("#dialog_confirm").find(
                                'input[name="circle_photo_choice_' + reqCircleId + '"]:checked'
                            );
                            var selectedPhotoId = $checked.val();
                            if (selectedPhotoId) {
                                // Build the JSON string for the hidden input
                                var hiddenValue = JSON.stringify({
                                    circle_id: circleId,
                                    photo_id: parseInt(selectedPhotoId),
                                });
                                var inputName = "selected_circles[" + reqCircleId + "]";

                                // Choose the container based on the select's class
                                var container;
                                if ($select.hasClass("existing-circle-change-select")) {
                                    container = $("#existing_circles_hidden_inputs");
                                } else if ($select.hasClass("existing-circle-select")) {
                                    container = $("#requested_circles_hidden_inputs");
                                } else {
                                    container = $("#requested_circles_hidden_inputs");
                                }

                                var $hiddenInput = $('input[name="' + inputName + '"]');
                                if ($hiddenInput.length === 0) {
                                    $hiddenInput = $("<input>", {
                                        type: "hidden",
                                        name: inputName,
                                    }).appendTo(container);
                                }
                                $hiddenInput.val(hiddenValue);

                                // Update the visible selected image below the search field.
                                // We assume you have a container with id "selected_image_<reqCircleId>"
                                var selectedPhotoUrl = $checked.data("url");
                                if (selectedPhotoUrl) {
                                    $("#selected_image_" + reqCircleId).html(
                                        '<img src="' +
                                        selectedPhotoUrl +
                                        '" alt="Selected Poster Photo" class="oe-image" style="margin-top:10px;" />'
                                    );
                                    //set visibility of the arrow symbol for circle's search
                                    $("#circle_search_arrow_" + reqCircleId).css("visibility", "visible");
                                }

                                // After adding/updating, check if all requested circles have been assigned
                                updateButtons();
                            }
                        });

                        // Insert the modal content into ActiveAdmins modal form container
                        $("#dialog_confirm").html(modalContent);
                    },
                });
            }
        });
    });
});

function updateButtons() {
    // Check circle assignment condition
    var totalRequested = $(".requested-circle-row").length;
    var totalHidden = $("#requested_circles_hidden_inputs input").length;
    var circlesReady = totalRequested === 0 || totalHidden === totalRequested;
    var hasRequestedCircles = totalRequested > 0;

    // Check remarks condition if a remarks field exists
    var remarksExists = $(".remarks-textarea").length > 0;
    var remarksFilled = remarksExists ? $(".remarks-textarea").val().trim().length > 0 : false;

    // Create warning message based on conditions
    let warningMessage = "";
    let submitDisabledReasons = [];

    // Collect reasons why submit button is disabled
    if (hasRequestedCircles && !circlesReady) {
        submitDisabledReasons.push("requested circles are present");
    }

    if (remarksFilled) {
        submitDisabledReasons.push("remarks are filled");
    }

    // Format the submit button warning message
    if (submitDisabledReasons.length > 0) {
        warningMessage = "*Submit button is disabled because " + submitDisabledReasons.join(" and ") + ". ";
    }

    // Add incomplete button warning if needed
    if (!remarksFilled) {
        if (warningMessage !== "") {
            warningMessage += " ";
        }
        warningMessage += "Incomplete button is disabled because remarks are required.";
    }

    // Enable the main submit button only if circles are ready and remarks field is empty.
    if (circlesReady && !remarksFilled) {
        $('input[type="submit"]:not(.incomplete-btn)').prop("disabled", false);
    } else {
        $('input[type="submit"]:not(.incomplete-btn)').prop("disabled", true);
    }

    // Enable the "Incomplete" button only if the remarks field has text.
    if (remarksFilled) {
        $(".incomplete-btn").prop("disabled", false);
    } else {
        $(".incomplete-btn").prop("disabled", true);
    }

    // Update warning message display - only on OE Work Flow page
    if (window.location.pathname.includes("/oe_work_flow")) {
        if (warningMessage !== "") {
            // Show warning with the combined message
            if ($(".submit-warning").length > 0) {
                // If the warning div exists, update its content and show it
                $(".submit-warning").html(warningMessage).fadeIn(300);
            } else {
                // If the warning div doesn't exist, create one near the submit buttons
                const form = $("form.formtastic");
                if (form.length > 0) {
                    const actions = form.find(".actions, .form-actions");
                    if (actions.length > 0) {
                        const warningDiv = $(
                            '<div class="submit-warning" style="color: red; margin-top: 10px;">' +
                            warningMessage +
                            "</div>"
                        );
                        actions.before(warningDiv);
                    }
                }
            }
        } else {
            // Hide warning if everything is good
            $(".submit-warning").fadeOut(300);
        }
    } else {
        // Hide all warnings on non-OE Work Flow pages
        $(".submit-warning").hide();
    }
}

$(document).ready(function () {
    // Hide warning messages on non-OE Work Flow pages
    if (!window.location.pathname.includes("/oe_work_flow")) {
        $(".submit-warning").hide();
    } else {
        // Only update buttons on OE Work Flow page
        updateButtons();
        // Note: Event handlers for remarks field are set in the DOMContentLoaded event above
    }
});
