// SASS variable overrides must be declared before loading up Active Admin's styles.
//
// To view the variables that Active Admin provides, take a look at
// `app/assets/stylesheets/active_admin/mixins/_variables.scss` in the
// Active Admin source.
//
// For example, to change the sidebar width:
// $sidebar-width: 242px;

// Active Admin's got SASS!
@import "active_admin/mixins";
@import "active_admin/base";

@import "active_admin/searchable_select";

// Dark scheme
@import "activeadmin-dark-color-scheme";

// Custom stylesheets
@import "boe_work_flow";

// Overriding any non-variable SASS must be done after the fact.
// For example, to change the default status-tag color:
//
//   .status_tag { background: #6090DB; }

@import "font-awesome";

input:disabled {
  opacity: 0.5;
}

.custom-label {
  color: green;
  font-weight: bold;
  font-size: medium;
  padding-left: 10px;
}

.oe-image {
  height: 200px;
  width: 200px;
  object-fit: contain;
}

.thumb_size {
  max-width: 200px;
}

.spacer {
  padding: 3px;
  margin: 5px 0;
}

.width-100 {
  width: 100%;
}

.no_event {
  display: none;
}

body.active_admin {
  .status_tag.opened, .status_tag.created, .status.auto_signed_up {
    background: steelblue;
  }

  .status_tag.last_transaction_failed, .status_tag.failed, .status_tag.banned, .status_tag.deleted {
    background: red;
  }

  .status_tag.pending, .status_tag.sending, .status_tag.pre_signup, .status_tag.unverified {
    background: orange;
    color: black;
  }

  .status_tag.successful, .status_tag.sent, .status_tag.active {
    background: green;
  }

  .status_tag.verified {
    color: green;
    border: 1px solid green;
  }

  /* Subscription Statuses */
  .status_tag.active {
    background: green;
  }

  .status_tag.cancelled, .status_tag.paused {
    background: red;
  }

  .status_tag.closed {
    background: grey;
  }

  .status_tag.created {
    background: steelblue;
  }

  /* Subscription charge Statuses */
  .status_tag.success {
    background: green;
  }

  .status_tag.refunded {
    background: grey;
  }

  .status_tag.failed {
    background: red;
  }

  .status_tag.pending, .status_tag.refund_initiated {
    background: orange;
    color: black;
  }

  .status_tag.created {
    background: steelblue;
  }
}

.activate-button {
  background-color: #4CAF50 !important; /* Green */
  border: 1px solid #45a049 !important;
  color: white !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
}

.activate-button:hover {
  background-color: #45a049 !important;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.activate-outline-button {
  background-color: white !important; /* Green */
  border: 1px solid #4CAF50 !important;
  color: #4CAF50 !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
}

.activate-outline-button:hover {
  background-color: #4CAF50 !important;
  color: white !important;
}

.danger-button {
  background-color: #dc3545 !important; /* red */
  color: white !important; /* Slightly darker red */
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
  position: relative;
}

.danger-button:hover {
  background-color: #bf2d3b !important;
}

.danger-outline-button {
  background-color: white !important;
  border: 1px solid #dc3545 !important;
  color: #dc3545 !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
  cursor: pointer;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  text-decoration: none !important;
}

.danger-outline-button:hover {
  background-color: #dc3545 !important; /* Green */
  color: white !important; /* Slightly darker red */
}

.disabled-button {
  background-color: #636363 !important; /* Grey */
  color: grey !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
}

.custom-tags-input {
  width: 100%;
  height: 100%;
  margin: 20px 10px;
  background: transparent;
  font-size: 1em;
  line-height: 1.5em;
  font-family: inherit;
  font-weight: inherit;
  resize: none;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tag-head-label {
  font-weight: bold;
  font-size: 1.2em;
  margin-bottom: 10px;
  text-decoration: underline
}

.tag-with-buttons {
  margin-left: 275px;
}

.tag-label {
  font-weight: bold;
  font-size: 1.1em;
  margin-bottom: 10px;
}

.tag-table {
  width: 230px;
}

.tag-table-column {
  width: 50px;
}

.tag-table-column-text {
  font-weight: bold;
  font-size: 1em;
}

.approve-button {
  background-color: white !important; /* Green */
  border: 1px solid #45a049 !important;
  color: #4CAF50 !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
}

.decline-button {
  background-color: white !important; /* Green */
  border: 1px solid #dc3545 !important;
  color: #dc3545 !important;
  transition-duration: 0.4s;
  text-shadow: none !important;
  font-weight: normal !important;
  background-image: none !important;
}

.approve-button.active {
  background-color: #45a049 !important;
  color: white !important;
}

.decline-button.active {
  background-color: #dc3545 !important;
  color: white !important;
}

.approve-button.inactive {
  background-color: white !important; /* Green */
  border: 1px solid #45a049 !important;
  color: #4CAF50 !important;
}

.decline-button.inactive {
  background-color: white !important; /* Green */
  border: 1px solid #dc3545 !important;
  color: #dc3545 !important;
}

.note-text {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.frame-table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.frame-table td {
  padding: 10px;
  border: 1px solid #ccc;
}

.frame-table td:first-child {
  width: 20%;
  text-align: center; /* Center text horizontally */
  vertical-align: middle;
}

.frame-table td:last-child {
  width: 80%;
}


.frame-label {
  font-weight: bold;
  font-size: 1.5em;
}

.package-frame-checkbox, .frame-add-on-checkbox, .status-frame-checkbox {
  display: flex;
  margin: 10px 0;
  gap: 10px;
}

.checkbox-frame-image {
  max-width: 200px;
  height: 200px;
  margin-right: 10px;
}

.premium-package-frame-list ol, .premium-frame-add-on-list ol, .status-frame-list ol {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.frames-checkbox {
  margin-right: 10px;
}

.frames-checkbox:disabled {
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.neutral-custom-label {
  color: orange;
  font-weight: bold;
  font-size: medium;
  padding-left: 10px;
}

/* Shadows and Depth */
.activate-button, .danger-button, .preview-button {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.danger-button:hover, .preview-button:hover {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* Pulse animation on hover for images */
@keyframes pulseEffect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.checkbox-frame-image:hover {
  animation: pulseEffect 1s infinite;
}

.admin_scripts input {
  padding: 5px 8px;
  margin-top: 3px;
}

.status_tag.in_use {
  background: #4CAF50; // Example: Green color for "IN USE"
  color: white;
}

.status_tag.not_in_use {
  background: #656464; // Example: Gray color for "NOT IN USE"
  color: white;
}

#dialog_confirm ul li label {
  color: black;
}

.content-container {
  display: flex;
}

.table-container {
  flex-grow: 1;
}

.filters-container {
  width: 200px;
  margin-left: 20px;
}

#filters {
  display: flex;
  flex-direction: column;
}

.filter-group {
  margin-bottom: 10px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
}

.filter-group input[type="text"],
.filter-group input[type="number"] {
  width: 100%;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.filter-group.buttons {
  display: flex;
  justify-content: space-between;
}

.poster-photos-div {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.poster-photo-section {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 24px; /* Space between columns */
  width: 100%;
  padding-left: 24px;
  padding-top: 16px;
}

.poster-photo-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 500;
}

.photos-section {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 20px; /* Space between columns */
}

.image-preview-parent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 10px;
}

.image-preview img {
  height: 280px;
  width: 250px;
  object-fit: contain;
}

#bg_removed_poster_photo {
  border: 1px solid #ccc;
}

.arrow-symbol {
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  margin-right: 40px;
}

/* Download button styling */
.download-btn {
  padding: 5px 10px;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
}

.download-btn:hover {
  background-color: #0056b3;
}

/* Each row is a flex container */
.requested-circle-row,
.existing-circle-row {
  display: flex;
  flex-direction: row;
  align-items: start;
  justify-content: flex-start;
  padding: 10px;
  border-bottom: 1px solid #ccc;
}

.circle-search-arrow-symbol {
  width: 50px;
  font-size: 2rem;
  display: flex;
  visibility: hidden;
  align-items: center;
  justify-content: center;
}

/* Circle Info: Name and image */
.circle-info {
  width: 350px;
  height: 300px;
  text-align: left;
  padding: 0 10px;
}

.circle-info h2 {
  font-weight: bold;
}

.requested-circle-download-btn {
  margin-left: 60px;
  padding-top: 10px;
  align-items: center;
}

.circle-search-parent {
  width: 350px;
  height: 300px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
}

.circle-search-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.circle-search {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: stretch;
}

.circle-search select {
  width: 80%;
  max-width: 300px;
  text-align: center;
}

.selected-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-actions {
  width: 350px;
  height: 300px;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.circle-actions .button {
  margin-bottom: 25px;
}

.remarks-parent {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
  gap: 15px;
}

.remarks-parent > .label {
  font-size: 1.2em;
  font-weight: bold;
}

.incomplete-btn:hover {
  background-color: #636363 !important; /* Grey */
  transition: transform 0.3s ease;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.incomplete-btn:disabled {
  opacity: 0.3;
}

.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.badge-text {
  padding: 15px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 1.5em;
  margin-right: 20px;
  min-height: 50px;
}

.badge-actions {
  display: flex;
  align-items: center;
}


.create-badge-button {
  background-color: #38598b;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.create-badge-button:hover {
  background-color: #2a4269;
}

.status_tag.badge_text {
  background-color: #4CAF50;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
}

.aspect-ratio-warning {
  margin-top: 5px;
  padding: 5px;
  background-color: #ffcccc;
  border-left: 3px solid #ff0000;
  border-radius: 3px;

  span {
    color: #ff0000;
    font-size: 12px;
  }
}

.aspect-ratio-info, .dummy-photo-info {
  span {
    color: #f0ad4e;
    font-size: 12px;
    font-style: italic;
    display: flex;
    align-items: center;
  }
}
